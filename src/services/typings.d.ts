declare namespace API {
  interface PageParams {
    size: number;
    page: number;
  }

  interface Result<T> {
    data: T;
  }

  type Success = null;

  // 与后端约定的响应数据格式
  interface Response {
    code: string | number;
    msg: string;
    data: any;
  }
}

declare namespace FormConfig {
  type Paths = (string | number)[] | string | number;
}

declare namespace Page {
  type BoundaryData = {
    boundaries: BMapGL.Point[][];
  };

  type NameItemType = {
    name: string;
    centroid: [number, number];
    adcode: number;
  }

  type ListItem = {
    /**
     * 名称
     */
    name: string;

    /**
     * 唯一标识
     */
    key: string;

    /**
     * 数值
     */
    value: number;
  }

  // 地铁线路列表项
  type SubwayLineItem = {
    /**
     * 地铁线名称
     */
    ln: string;

    /**
     * 颜色
     */
    cl: string;

    /**
     * 地铁线路线
     */
    st: {
      /**
       * 经纬度
       */
      sl: string;

      /**
       * 站点名称
       */
      n: string;

      /**
       * 站点id
       */
      poiid: string;
    }[]

    /**
    * 唯一标识
    */
    li: string;
  }

  interface SubwayMapData {
    /**
     * 地铁线
     */
    line: BMapGL.Polyline;

    /**
     * 站点
     */
    station: BMapGL.Marker[];

    /**
     * 站点信息
     */
    label: BMapGL.Label[];
  }

  interface SubwayMapItem {
    /**
     * 地铁线路名称 1号线
     */
    name: string;

    /**
     * 地铁线颜色
     */
    color: string;

    /**
     * 地铁线
     */
    line: BMapGL.Point[];

    /**
     * 站点
     */
    stationList: {
      position: BMapGL.Point;

      /**
       * 站点名称
       */
      name: string;
    }[];
  }

  interface CommunityMapItem {
    /**
     * 住宅小区名称
     */
    name: string;

    /**
     * 住宅小区经纬度
     */
    location: {
      lat: number;
      lng: number;
    };

    /**
     * uid
     */
    uid: string
  }

  interface CommunityBoundaryData {
    /**
     * 小区唯一标识
     */
    uid: string;

    /**
     * 小区名称
     */
    name: string;

    /**
     * 小区地址
     */
    address: string;

    /**
     * 百度地图边界几何数据(米制坐标)
     */
    geo: string;
  }

  interface SubwayDataResult {
    /**
     * 省市地铁
     */
    s: string;

    /**
     * code
     */
    i: string;

    l: SubwayLineItem[];
  }

  export interface DivisionData {
    /**
     * 省市区编码
     */
    v: string;
    /**
     * 省市区名称
     */
    n: string;
    /**
     * 省市区子节点
     */
    c?: DivisionData[];
  }

  export interface DivisionTreeOption {
    /**
     * 省市区编码
     */
    value: string;
    /**
     * 省市区名称
     */
    label: string;
    /**
    * 省市区子节点
    */
    children?: DivisionTreeOption[];
  }
}

declare module 'react-map-interaction' {
  export type Translation = {
    x: number;
    y: number;
  };

  export type ScaleTranslation = {
    scale: number;
    translation: Translation;
  };

  export type MapInteractionProps = {
    children?: (scaleTranslation: ScaleTranslation) => React.ReactNode;

    value?: ScaleTranslation;

    defaultValue?: ScaleTranslation;

    disableZoom?: boolean;

    disablePan?: boolean;

    translationBounds?: {
      xMin?: number;
      xMax?: number;
      yMin?: number;
      yMax?: number;
    };

    onChange?: (scaleTranslation: ScaleTranslation) => void;

    minScale?: number;
    maxScale?: number;

    showControls?: boolean;

    plusBtnContents?: React.ReactNode;
    minusBtnContents?: React.ReactNode;

    controlsClass?: string;

    btnClass?: string;

    plusBtnClass?: string;
    minusBtnClass?: string;
  };
  export const MapInteraction: React.FC<MapInteractionProps>;

  export type MapInteractionCSSProps = React.PropsWithChildren<Omit<MapInteractionProps, 'children'>>;
  export const MapInteractionCSS: React.FC<MapInteractionCSSProps>;

  export default MapInteraction;
}
