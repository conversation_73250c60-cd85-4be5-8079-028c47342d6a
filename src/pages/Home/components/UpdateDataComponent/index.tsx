import { EncodingEnum } from "@/enum";
import { getRegionTypeByAdCode } from "@/pages/Home1/utils";
import useStore from "@/store";
import { useImmerAtom } from "jotai-immer";
import { useMemoizedFn, useMount, useRequest } from "ahooks";
import { memo, useMemo, useRef } from "react";
import { railwayCityData } from "@/data/railwayData";
import { getAdCodePath } from "@/data/provinceData";
import { subwayMapAtom } from "@/store/atom";

function UpdateDataComponent() {
  const subwayMapRef = useRef<BMapGL.Map | null>(null);

  const mapRefState = useStore((state) => state.mapRefState);

  const domRef = useRef<HTMLDivElement>(null);

  const [subwayMap, setSubwayMap] = useImmerAtom(subwayMapAtom);

  const searchCode = useStore((state) => {
    const selectCode = state.selectCode.toString();
    const code = getAdCodePath(state.selectCode.toString())?.[0]?.value;
    const type = getRegionTypeByAdCode(code);
    if (type === EncodingEnum.MeCity) {
      // 直辖市
      return code?.slice(0, 4);
    } else if (
      type === EncodingEnum.Province ||
      type === EncodingEnum.District
    ) {
      // 最外层是省份或者区 返回选中的前4位
      return selectCode.slice(0, 4);
    }
    return undefined;
  });

  const ready = useMemo(() => {
    if (searchCode && !!railwayCityData[searchCode]) {
      return !subwayMap.has(searchCode);
    }
    return false;
  }, [searchCode, subwayMap]);

  const cityName = useMemo(() => {
    return searchCode ? railwayCityData[searchCode]?.name : "";
  }, [searchCode]);

  useMount(() => {
    subwayMapRef.current = new BMapGL.Map(domRef.current!);
  });

  // 获取地铁线路数据 存储map备用
  const onSaveSubwayLie = useMemoizedFn(
    (code: string, name: string, item: Page.SubwayLineItem) => {
      const lineName = item.ln;
      const busLineSearch = new BMapGL.BusLineSearch(name, {
        renderOptions: {
          map: subwayMapRef.current!,
          selectFirstResult: true,
          highlightMode: 2,
        },
        onGetBusListComplete: (result) => {
          if (result?.getBusListItem) {
            const firstLine = result.getBusListItem(0);
            if (firstLine) {
              busLineSearch.getBusLine(firstLine);
            }
          }
        },
        onGetBusLineComplete: (result) => {
          if (result) {
            // 获取地铁线路的路径点
            const polyline = result.getPath();
            // 定义地铁线路颜色
            const lineColor = item?.cl ? `#${item?.cl}` : "#339AFF"; // 可以根据不同线路设置不同颜色
            const subwayMapData: Page.SubwayMapItem = {
              line: polyline,
              stationList: [],
              name: item.ln,
              color: lineColor,
            };
            // 获取并绘制地铁站点
            const stationCount = Number(result.getNumBusStations());
            for (let i = 0; i < stationCount; i++) {
              const station = result.getBusStation(i);
              subwayMapData.stationList.push({
                name: station.name,
                position: station.position,
              });
            }
            // 把路线数据存储到map中
            setSubwayMap((draft) => {
              const subwayList = draft.get(code) ?? [];
              subwayList.push(subwayMapData);
            });
          }
        },
      });
      // 开始搜索
      busLineSearch.getBusList(lineName);
    }
  );

  // 地铁线路数据存储
  const onSaveSubwayMapState = useMemoizedFn((list: Page.SubwayLineItem[]) => {
    const zoom = mapRefState?.getZoom();
    const center = mapRefState?.getCenter();
    // 初始化信息
    setSubwayMap((draft) => {
      draft.set(searchCode!, []);
    });
    if (zoom) {
      // @ts-expect-error
      subwayMapRef.current?.setZoom(zoom, { noAnimation: true });
    }
    if (center) {
      // @ts-expect-error
      subwayMapRef.current?.setCenter(center, { noAnimation: true });
    }
    list?.forEach((item) => {
      onSaveSubwayLie(searchCode!, cityName, item);
    });
  });

  useRequest(
    async (): Promise<Page.SubwayDataResult> => {
      try {
        const res = await fetch(`/screen/subwayData/${searchCode}00.json`, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        });
        return res.json();
      } catch (error) {
        console.error("请求失败:", error);
        throw error;
      }
    },
    {
      ready,
      refreshDeps: [searchCode],
      onSuccess: (res) => {
        // 获取地铁线地图数据 存储
        onSaveSubwayMapState(res.l ?? []);
      },
    }
  );
  return <div ref={domRef} className="fixed w-0 h-0 hidden"></div>;
}
export default memo(UpdateDataComponent);
