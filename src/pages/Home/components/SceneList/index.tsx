import Card from "@/components/Card";
import { AbilityEnum, SceneTypeEnum } from "@/enum";
import useStore from "@/store";
import { memo, useMemo } from "react";
import clsx from "clsx";
import { useMemoizedFn } from "ahooks";

function SceneList() {
  const selectAbilityType = useStore((state) => state.selectAbilityType);

  const getValue = useMemoizedFn((sceneType: SceneTypeEnum) => {
    const value: Record<SceneTypeEnum, Partial<Record<AbilityEnum, number>>> = {
      [SceneTypeEnum.District]: {
        [AbilityEnum.UserPriorityScheduling]: 0,
        [AbilityEnum.BusinessBandwidthGuarantee]: 0,
        [AbilityEnum.UserDynamicSpeedLimit]: 0,
      },
      [SceneTypeEnum.Subway]: {
        [AbilityEnum.UserPriorityScheduling]: 188,
        [AbilityEnum.BusinessBandwidthGuarantee]: 29,
        [AbilityEnum.UserDynamicSpeedLimit]: 23,
      },
      [SceneTypeEnum.HighSpeedTrainStation]: {
        [AbilityEnum.UserPriorityScheduling]: 0,
        [AbilityEnum.BusinessBandwidthGuarantee]: 0,
        [AbilityEnum.UserDynamicSpeedLimit]: 0,
      },
      [SceneTypeEnum.ResidentialCommunity]: {
        [AbilityEnum.UserPriorityScheduling]: 300,
        [AbilityEnum.BusinessBandwidthGuarantee]: 54,
        [AbilityEnum.UserDynamicSpeedLimit]: 200,
      },
      [SceneTypeEnum.KeyTransportation]: {
        [AbilityEnum.UserPriorityScheduling]: 199,
        [AbilityEnum.BusinessBandwidthGuarantee]: 48,
        [AbilityEnum.UserDynamicSpeedLimit]: 99,
      },
      [SceneTypeEnum.TransportationHubs]: {
        [AbilityEnum.UserPriorityScheduling]: 199,
        [AbilityEnum.BusinessBandwidthGuarantee]: 32,
        [AbilityEnum.UserDynamicSpeedLimit]: 9,
      },
      [SceneTypeEnum.KeyShopping]: {
        [AbilityEnum.UserPriorityScheduling]: 76,
        [AbilityEnum.BusinessBandwidthGuarantee]: 25,
        [AbilityEnum.UserDynamicSpeedLimit]: 54,
      },
      [SceneTypeEnum.BusinessBuilding]: {
        [AbilityEnum.UserPriorityScheduling]: 82,
        [AbilityEnum.BusinessBandwidthGuarantee]: 20,
        [AbilityEnum.UserDynamicSpeedLimit]: 23,
      },
      [SceneTypeEnum.GovernmentCenter]: {
        [AbilityEnum.UserPriorityScheduling]: 63,
        [AbilityEnum.BusinessBandwidthGuarantee]: 18,
        [AbilityEnum.UserDynamicSpeedLimit]: 23,
      },
      [SceneTypeEnum.HighSchool]: {
        [AbilityEnum.UserPriorityScheduling]: 61,
        [AbilityEnum.BusinessBandwidthGuarantee]: 18,
        [AbilityEnum.UserDynamicSpeedLimit]: 3,
      },
      [SceneTypeEnum.CultureAndTourism]: {
        [AbilityEnum.UserPriorityScheduling]: 59,
        [AbilityEnum.BusinessBandwidthGuarantee]: 16,
        [AbilityEnum.UserDynamicSpeedLimit]: 8,
      },
      [SceneTypeEnum.MedicalInstitution]: {
        [AbilityEnum.UserPriorityScheduling]: 53,
        [AbilityEnum.BusinessBandwidthGuarantee]: 12,
        [AbilityEnum.UserDynamicSpeedLimit]: 53,
      },
    };
    return value[sceneType][selectAbilityType];
  });

  const list = useMemo(() => {
    switch (selectAbilityType) {
      case AbilityEnum.UserPriorityScheduling:
        return [
          {
            name: "住宅小区",
            value: 200,
            key: SceneTypeEnum.ResidentialCommunity,
          },
          {
            name: "重点交通干线",
            value: 99,
            key: SceneTypeEnum.KeyTransportation,
          },
          {
            name: "交通枢纽",
            value: 9,
            key: SceneTypeEnum.TransportationHubs,
          },
          {
            name: "地铁",
            value: 23,
            key: SceneTypeEnum.Subway,
          },
          {
            name: "重点商超",
            value: 54,
            key: SceneTypeEnum.KeyShopping,
          },
          {
            name: "商务楼宇",
            value: 23,
            key: SceneTypeEnum.BusinessBuilding,
          },
          {
            name: "政务中心",
            value: 23,
            key: SceneTypeEnum.GovernmentCenter,
          },
          {
            name: "高等学校",
            value: 3,
            key: SceneTypeEnum.HighSchool,
          },
          {
            name: "文旅景区",
            value: 22,
            key: SceneTypeEnum.CultureAndTourism,
          },
          {
            name: "医疗机构",
            value: 53,
            key: SceneTypeEnum.MedicalInstitution,
          },
        ];

      default:
        break;
    }
    return [
      {
        name: "住宅小区",
        value: 200,
        key: SceneTypeEnum.ResidentialCommunity,
      },
      {
        name: "重点交通干线",
        value: 99,
        key: SceneTypeEnum.KeyTransportation,
      },
      {
        name: "交通枢纽",
        value: 9,
        key: SceneTypeEnum.TransportationHubs,
      },
      {
        name: "地铁",
        value: 23,
        key: SceneTypeEnum.Subway,
      },
      {
        name: "重点商超",
        value: 54,
        key: SceneTypeEnum.KeyShopping,
      },
      {
        name: "商务楼宇",
        value: 23,
        key: SceneTypeEnum.BusinessBuilding,
      },
      {
        name: "政务中心",
        value: 23,
        key: SceneTypeEnum.GovernmentCenter,
      },
      {
        name: "高等学校",
        value: 3,
        key: SceneTypeEnum.HighSchool,
      },
      {
        name: "文旅景区",
        value: 22,
        key: SceneTypeEnum.CultureAndTourism,
      },
      {
        name: "医疗机构",
        value: 53,
        key: SceneTypeEnum.MedicalInstitution,
      },
    ];
  }, [selectAbilityType]);

  const sceneType = useStore((state) => state.sceneType);

  return (
    <Card
      title="十大场景"
      className="absolute w-[554px] right-[1536px] top-[280px]"
    >
      <div className="h-[1674px] py-[40px] px-[24px] space-y-[32px]">
        {list.map((val) => (
          <div
            className={clsx(
              "px-[8px] h-[130px] cursor-pointer rounded-[12px] text-[rgba(255,255,255,0.85)] text-[36px] flex items-center justify-between",
              {
                "hover:bg-[rgba(255,255,255,0.05)]": sceneType !== val.key,
                "bg-[rgba(95,234,255,0.15)]": sceneType === val.key,
              }
            )}
            key={val.key}
            onClick={() => {
              useStore.setState((draft) => {
                draft.sceneType =
                  val.key === draft.sceneType ? undefined : val.key;
              });
            }}
          >
            <span className="pl-[32px]">{val.name}</span>
            <div className="flex items-center">
              <span className="text-[42px] text-white">
                {getValue(val.key)}
              </span>
              <span className="pr-[24px]">万个</span>
            </div>
          </div>
        ))}
      </div>
    </Card>
  );
}
export default memo(SceneList);
