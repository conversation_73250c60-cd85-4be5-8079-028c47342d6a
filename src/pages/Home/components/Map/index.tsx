import React, { memo, useRef } from "react";
import { Map } from "react-bmapgl";
import mapJson from "@/assets/mapStyle.json";
import { useCreation, useEventListener, useMemoizedFn } from "ahooks";
import { convertGeoJSONToPolygonData, type PolygonData } from "@/utils";
import { useRequest } from "ahooks";
import { useSetAtom } from "jotai";
import useStore from "@/store";
import { mapLegendData } from "@/data/mapLegendData";
import { EncodingEnum, SceneTypeEnum } from "@/enum";
import { mapPolygonAtom } from "@/store/atom";
import { getRegionTypeByAdCode } from "@/pages/Home1/utils";

function BaiduMap() {
  const mapRef = useRef<BMapGL.Map | undefined>(undefined);

  const selectCode = useStore((state) => state.selectCode);

  const loading = useStore((state) => state.loading);

  // 选中地铁场景线路展示
  const selectedSubway = useStore(
    (state) => state.sceneType === SceneTypeEnum.Subway
  );

  const setMapPolygon = useSetAtom(mapPolygonAtom);

  // 底图瓦片加载完成
  useEventListener(
    "tilesloaded",
    () => {
      useStore.setState((draft) => {
        draft.loading = false;
      });
    },
    { target: mapRef as any }
  );

  const onInitLabels = (areaData: any) => {
    const value: Page.NameItemType[] =
      areaData?.features?.map?.((item: any) => ({
        name: item.properties?.name,
        centroid: item.properties?.centroid ?? item.properties?.center,
        adcode: item.properties?.adcode,
      })) ?? [];

    const nameList = value?.filter((val) => val.name && val.centroid);

    const labels = nameList.reduce<BMapGL.Label[]>((acc, val) => {
      const label = new BMapGL.Label(val.name, {
        position: new BMapGL.Point(val.centroid[0], val.centroid[1]),
        offset: new BMapGL.Size(-16, 0),
      });

      label.setStyle({
        fontSize: "0.44vw",
        color: "rgba(255,255,255,0.65)",
        background: "none",
        fontWeight: "normal",
        border: "none",
      });
      acc.push(label);
      return acc;
    }, []);

    // 批量添加到地图
    labels.forEach((label) => mapRef.current?.addOverlay(label));
  };

  // 地图聚焦视野
  const onViewport = (mapData: any) => {
    // 是不是全国
    const isChina = selectCode === 100000;

    const polygonMapData = convertGeoJSONToPolygonData(mapData, true);

    // 收集所有多边形的点坐标，用于设置视野
    const pointArray: BMapGL.Point[] = [];

    polygonMapData.forEach((polygonData: PolygonData) => {
      // 将每个多边形的路径点添加到数组中
      pointArray.push(...polygonData.path);
    });

    const marginTopValue = isChina ? 0 : 50;

    if (pointArray.length > 0) {
      // 调整视野以包含所有点
      const viewport = mapRef.current?.getViewport(pointArray, {
        enableAnimation: false,
        delay: 0,
        zoomFactor: isChina ? -0.1 : 0.3,
        margins: [marginTopValue, 100, 0, 0],
      });
      // @ts-expect-error
      mapRef.current?.setCenter(viewport?.center, { noAnimation: true });
      // @ts-expect-error
      mapRef.current?.setZoom(viewport.zoom, { noAnimation: true });
    }
  };

  const onInitPolygonMap = useMemoizedFn((mapData: any) => {
    // 省市Polygon渲染数据 渲染整体的那个 用于边框颜色加重
    const polygonMapData = convertGeoJSONToPolygonData(mapData, true);

    const polygons = polygonMapData.reduce<BMapGL.Polygon[]>((acc, item) => {
      const polygon = new BMapGL.Polygon(item.path, {
        strokeColor: selectedSubway ? "#5FEAFF" : "#FFFFFF",
        strokeWeight: selectedSubway ? 2 : 1.2,
        strokeOpacity: 1,
        fillColor: selectedSubway ? "#000000" : "#98d2f6",
        fillOpacity: selectedSubway ? 0.3 : 1,
      });
      acc.push(polygon);
      return acc;
    }, []);
    setMapPolygon(polygons?.[0]);
    polygons.forEach((polygon) => {
      mapRef.current?.addOverlay(polygon);
    });
  });

  const onInitPolygonRegion = (areaData: any) => {
    // 转换 GeoJSON 数据为 Polygon 组件需要的格式 拆分为多个区域渲染
    const list = convertGeoJSONToPolygonData(areaData);
    const polygons = list.reduce<BMapGL.Polygon[]>((acc, item, index) => {
      const polygon = new BMapGL.Polygon(item.path, {
        strokeColor: "#FFFFFF",
        strokeWeight: 0.8,
        strokeOpacity: 0.4,
        fillColor: mapLegendData[index % mapLegendData.length].color,
        fillOpacity: 1,
      });
      acc.push(polygon);
      polygon.onclick = () => {
        // 获取当前城市 下钻
        useStore.setState((draft) => {
          draft.selectCode = item.properties.adcode;
        });
      };
      return acc;
    }, []);
    polygons.forEach((polygon) => {
      mapRef.current?.addOverlay(polygon);
    });
  };

  const { data } = useRequest(
    async () => {
      try {
        // 判断是不是街道
        const isStreet =
          getRegionTypeByAdCode(selectCode) === EncodingEnum.Street;
        const [resFull, resData] = await Promise.all([
          // 区和街道不需要渲染内部的区域
          fetch(
            `/screen/areaData/${
              isStreet ? "street/" : ""
            }${selectCode}_full.json`,
            {
              method: "GET",
              headers: {
                "Content-Type": "application/json",
              },
            }
          ),
          isStreet
            ? Promise.resolve(null)
            : fetch(`/screen/mapData/${selectCode}.json`, {
                method: "GET",
                headers: {
                  "Content-Type": "application/json",
                },
              }),
        ]);
        const areaData = resFull ? await resFull.json() : null;
        const mapData = resData ? await resData.json() : null;
        return { areaData, mapData: isStreet ? areaData : mapData };
      } catch (error) {
        console.error("请求失败:", error);
        throw error;
      }
    },
    {
      refreshDeps: [selectCode],
    }
  );

  // 绘制地图
  const onInitMapComponent = useMemoizedFn(() => {
    mapRef.current?.clearOverlays();
    if (data?.mapData) {
      onInitPolygonMap(data.mapData);
      onViewport(data.mapData);
    }
    if (data?.areaData) {
      if (!selectedSubway) {
        // 选中了地铁线路 不绘制区域
        onInitPolygonRegion(data.areaData);
        onInitLabels(data.areaData);
      }
    }
  });

  useCreation(() => {
    // onInitMapComponent();
  }, [data, selectedSubway]);

  return (
    <React.Fragment>
      <div
        className="absolute inset-x-0 inset-y-0 z-0 flex items-center justify-center"
        style={{
          opacity: loading ? 0 : 1,
        }}
      >
        <Map
          ref={(value) => {
            mapRef.current = value?.map;
            useStore.setState((draft) => {
              draft.mapRefState = value?.map;
            });
          }}
          style={{
            width: "100%",
            height: "100%",
          }}
          mapStyleV2={{
            styleJson: mapJson,
          }}
          center={new BMapGL.Point(116.405285, 39.904989)}
          zoom={5}
          heading={0}
          tilt={0}
          // 允许拖拽
          enableDragging
          enableScrollWheelZoom
        />
      </div>
    </React.Fragment>
  );
}

export default memo(BaiduMap);
