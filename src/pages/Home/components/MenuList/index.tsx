import { memo, useMemo } from "react";
import { AbilityEnum } from "@/enum";
import useStore from "@/store";
import { Segmented } from "antd";
import React from "react";
import { mapLegendData } from "@/data/mapLegendData";
import IconFont from "@/components/IconFont";
import { useInterval } from "ahooks";
import { CHINA_CODE } from "@/constant";

function MenuList() {
  const selectAbilityType = useStore((state) => state.selectAbilityType);

  const options = useMemo(() => {
    return [
      {
        value: AbilityEnum.UserPriorityScheduling,
        label: "用户优先级调度",
      },
      {
        value: AbilityEnum.BusinessBandwidthGuarantee,
        label: "业务带宽保障",
      },
      {
        value: AbilityEnum.UserDynamicSpeedLimit,
        label: "用户动态限速",
      },
    ];
  }, []);

  // 统计列表
  const statisticsList = useMemo(() => {
    switch (selectAbilityType) {
      case AbilityEnum.UserPriorityScheduling:
        return [
          {
            name: "400优先级",
            value: 4300,
            icon: "icon9",
          },
          {
            name: "600优先级",
            value: 1340,
            icon: "icon9",
          },
          {
            name: "800优先级",
            value: 970,
            icon: "icon9",
          },
          {
            name: "800-1000优先级",
            value: 325,
            icon: "icon9",
          },
          {
            name: "1000以上优先级",
            value: 170,
            icon: "icon9",
          },
        ];
      case AbilityEnum.BusinessBandwidthGuarantee:
        return [
          {
            name: "游戏级别",
            value: 120,
            icon: "icon10",
          },
          {
            name: "直播级别",
            value: 96,
            icon: "icon11",
          },
          {
            name: "会议级别",
            value: 55,
            icon: "icon12",
          },
        ];
      case AbilityEnum.UserDynamicSpeedLimit:
        return [
          {
            name: "1kbps限速",
            value: 522,
            icon: "icon13",
          },
          {
            name: "20kbps限速",
            value: 134,
            icon: "icon13",
          },
          {
            name: "128kbps-1Mbps限速（冰激凌）",
            value: 97,
            icon: "icon13",
          },
          {
            name: "1-2Mbps限速（冰激凌)",
            value: 32,
            icon: "icon13",
          },
          {
            name: "2-7.2Mbps限速（冰激凌）",
            value: 17,
            icon: "icon13",
          },
        ];

      default:
        break;
    }
    return [];
  }, [selectAbilityType]);

  const isChina = useStore((state) => state.selectCode === 100000);

  useInterval(
    () => {
      // 自动切换options 轮播
      useStore.setState((draft) => {
        if (draft.selectAbilityType === AbilityEnum.UserPriorityScheduling) {
          draft.selectAbilityType = AbilityEnum.BusinessBandwidthGuarantee;
        } else if (
          draft.selectAbilityType === AbilityEnum.BusinessBandwidthGuarantee
        ) {
          draft.selectAbilityType = AbilityEnum.UserDynamicSpeedLimit;
        } else if (
          draft.selectAbilityType === AbilityEnum.UserDynamicSpeedLimit
        ) {
          draft.selectAbilityType = AbilityEnum.UserPriorityScheduling;
        }
      });
    },
    isChina ? 5000 : undefined
  );

  return (
    <React.Fragment>
      <Segmented
        className="x-centered top-[52px] z-40 backdrop-blur-[16px] pointer-events-auto"
        options={options}
        value={selectAbilityType}
        onChange={(value) => {
          useStore.setState((draft) => {
            draft.selectAbilityType = value;
            draft.selectCode = CHINA_CODE;
            draft.sceneType = undefined;
          });
        }}
      />
      <div className="absolute right-[2190px] space-y-[40px] y-centered mt-[110px] pointer-events-auto">
        {statisticsList.map((val, index) => (
          <div className="flex min-h-[160px]" key={`statistics-${index}`}>
            <div className="w-[165px] self-stretch rounded-[24px_0_0_24px] bg-[#2A4160] flex items-center justify-center">
              <IconFont
                type={`icon-${val.icon}`}
                className="text-[74px] text-[#5FEAFF]!"
              />
            </div>
            <div className="w-[420px] self-stretch ml-3 rounded-[0_24px_24px_0] bg-[#2A4160] flex justify-center flex-col px-[26px] py-4">
              <span className="text-[40px] text-white mt-1 leading-[60px]">
                {val.name}
              </span>
              <div className="flex items-baseline mt-1">
                <span className="text-[#5FEAFF] text-[52px] value-font">
                  {val.value}
                </span>
                <span className="text-[26px] ml-4">万个</span>
              </div>
            </div>
          </div>
        ))}
      </div>
      <div className="absolute shadow-[0px_0px_10px_1px_rgba(0,0,0,0.04)] bottom-[100px] left-[2084px] w-[330px] px-[54px] py-[36px] bg-[#2A4160] space-y-[24px] pointer-events-auto">
        {mapLegendData.map((val, index) => (
          <div
            className="h-[54px] flex items-center"
            key={`legend${index + 1}`}
          >
            <div
              className="w-[56px] h-[30px]"
              style={{
                background: val.color,
              }}
            ></div>
            <div className="ml-5 text-[26px]">
              {val.min}-{val.max}&nbsp;{val.unit}
            </div>
          </div>
        ))}
      </div>
    </React.Fragment>
  );
}
export default memo(MenuList);
