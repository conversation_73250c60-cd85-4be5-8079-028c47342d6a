import React, { memo } from "react";
import NetworkSituation from "./components/NetworkSituation";
import NetworkQuality from "./components/NetworkQuality";
import ProvinceTop10 from "./components/ProvinceTop10";
import ConnectionStatus from "./components/ConnectionStatus";
import ConditionService from "./components/ConditionService";
import CurrentAlarm from "./components/CurrentAlarm";

/**
 * 大左和大右公共的
 */
function Common() {
  return (
    <React.Fragment>
      {/* 网络情况 */}
      <NetworkSituation />
      {/* 网络连接质量 */}
      <NetworkQuality />
      {/* 昨日分省连接Top10 */}
      <ProvinceTop10 />
      {/* 用户连接情况 */}
      <ConnectionStatus />
      {/* 连接使用情况 */}
      <ConditionService />
      {/* 当前告警 */}
      <CurrentAlarm />
    </React.Fragment>
  );
}
export default memo(Common);
