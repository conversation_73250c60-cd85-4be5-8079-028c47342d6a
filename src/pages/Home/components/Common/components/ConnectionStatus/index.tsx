import React, { useMemo, useRef } from "react";
import type { EChartsInstance } from "echarts-for-react";
import ReactECharts from "echarts-for-react";
import "echarts-gl";
import usePieOptions from "./hooks/usePieOptions";
import { getPercent } from "@/utils";
import icon1 from "@/assets/images/bgIcon1.png";
import icon2 from "@/assets/images/bgIcon2.png";
import icon3 from "@/assets/images/bgIcon3.png";
import {
  XAxis,
  // Tooltip,
  CartesianGrid,
  YAxis,
  Legend,
  BarChart,
  Bar,
} from "recharts";
import Card from "@/components/Card";

function ConnectionStatus() {
  const chartRef = useRef<EChartsInstance>();

  const statusList = useMemo(() => {
    return [
      {
        label: "用户优先级调度",
        value: "6752",
        color: "#00E4BA",
        icon: icon1,
      },
      {
        label: "业务带宽保障",
        value: "367",
        color: "#00DEF5",
        icon: icon2,
      },
      {
        label: "用户动态限速",
        value: "15433",
        color: "#DEBB79",
        icon: icon3,
      },
    ];
  }, []);

  const statusListPie = useMemo(() => {
    return [
      {
        label: "用户优先级调度",
        key: "y1",
        value: "3410",
        color: "#00E4BA",
      },
      {
        label: "业务带宽保障",
        key: "y2",
        value: "3050",
        color: "#00DEF5",
      },
      {
        label: "用户动态限速",
        key: "y3",
        value: "3540",
        color: "#DEBB79",
      },
    ];
  }, []);

  const enumKey = useMemo(() => {
    return statusListPie.reduce((acc, item) => {
      acc[item.key] = {
        value: item.label,
        color: item.color,
      };
      return acc;
    }, {} as Record<string, { value: string; color: string }>);
  }, [statusListPie]);

  const barData = useMemo(() => {
    return [
      {
        x: "1月",
        y1: 2800,
        y2: 1000,
        y3: 2100,
      },
      {
        x: "2月",
        y1: 3100,
        y2: 1000,
        y3: 2200,
      },
      {
        x: "3月",
        y1: 3300,
        y2: 1000,
        y3: 3800,
      },
      {
        x: "4月",
        y1: 2800,
        y2: 1000,
        y3: 2600,
      },
      {
        x: "5月",
        y1: 3200,
        y2: 1000,
        y3: 2800,
      },
      {
        x: "6月",
        y1: 3200,
        y2: 1000,
        y3: 2800,
      },
      {
        x: "7月",
        y1: 3000,
        y2: 1000,
        y3: 2600,
      },
      {
        x: "8月",
        y1: 2800,
        y2: 1000,
        y3: 2600,
      },
      {
        x: "9月",
        y1: 3100,
        y2: 1000,
        y3: 3600,
      },
      {
        x: "10月",
        y1: 1200,
        y2: 1000,
        y3: 2600,
      },

      {
        x: "11月",
        y1: 3100,
        y2: 1000,
        y3: 2900,
      },
      {
        x: "12月",
        y1: 2500,
        y2: 1000,
        y3: 3200,
      },
    ];
  }, []);

  const sumValue = useMemo(() => {
    return statusListPie.reduce((acc, item) => acc + Number(item.value), 0);
  }, [statusListPie]);

  const option = usePieOptions({
    data: statusListPie.map((item) => ({
      name: item.label,
      value: Number(item.value),
      color: item.color,
    })),
    activeIndex: 0,
    activeHeight: 6000,
    externalRadius: 0.2,
    ladderHeight: (value: number) => value,
  });

  return (
    <React.Fragment>
      <Card
        title="用户连接情况"
        className="absolute w-[1336px] right-[100px] top-[280px]"
      >
        <div className="h-[490px]">
          <div className="h-full flex flex-col pt-[56px] pb-[12px] px-[56px]">
            <div className="flex justify-between">
              <div className="flex justify-between w-[650px]">
                {statusList.map((item, index) => {
                  return (
                    <div
                      key={index}
                      className="h-[180px] flex flex-col relative"
                    >
                      <div className="flex justify-center gap-[16px]">
                        <div
                          className="text-[42px] leading-[64px]"
                          style={{
                            color: item.color,
                          }}
                        >
                          {item.value}
                        </div>
                        <div className="text-[24px] leading-[68px]">万户</div>
                      </div>
                      <div className="text-[26px] text-center">
                        {item.label}
                      </div>
                      <div className="absolute bottom-0 left-[50%] translate-x-[-50%] w-[115px] h-[130px]">
                        <img src={item.icon} alt="" className="w-[115px] h-[130px]" />
                      </div>
                    </div>
                  );
                })}
              </div>
              <div className="h-[150px] flex gap-[24px]">
                <ReactECharts
                  ref={chartRef}
                  option={option}
                  opts={{ devicePixelRatio: 2.5 }}
                  style={{ width: 250, height: 150 }}
                />
                <div className="w-[268px] flex flex-col justify-center gap-[14px]">
                  {statusListPie.map((item) => {
                    return (
                      <div
                        key={item.label}
                        className="flex items-center gap-[16px]"
                      >
                        <div
                          className="w-[16px] h-[16px]"
                          style={{ backgroundColor: item.color }}
                        ></div>
                        <div className="text-[17px] flex-1">{item.label}</div>
                        <div className="text-[24px]">
                          {getPercent(Number(item.value), sumValue)}%
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
            <div className="h-[260px]">
              <BarChart
                width={1300}
                height={260}
                margin={{
                  right: 80,
                  top: 40,
                  bottom: 20,
                }}
                data={barData}
                barGap="0%"
                barCategoryGap={30}
              >
                <CartesianGrid
                  vertical={false}
                  strokeDasharray="3 3"
                  color="#556780"
                />
                <XAxis dataKey="x" stroke="#fff" tickLine={false} />
                <YAxis
                  stroke="#fff"
                  tickLine={false}
                  axisLine={false}
                  label={{
                    value: "单位:万户",
                    color: "#fff",
                    position: "insideTop",
                    fontSize: 18,
                    fontWeight: 500,
                    fontFamily: "PingFang SC",
                    dx: 10,
                    dy: -40,
                    angle: 0,
                    fill: "#fff",
                  }}
                />
                {/* <Tooltip /> */}
                <Legend
                  verticalAlign="top"
                  height={36}
                  align="right"
                  content={(e) => {
                    const payload = e.payload;
                    return (
                      <React.Fragment>
                        <div className="flex gap-[16px] justify-end translate-y-[-15px]">
                          {payload?.map((item) => {
                            return (
                              <div key={item.value} className="flex items-center gap-[16px]">
                                <div className="w-[16px] h-[16px]" style={{ backgroundColor: enumKey[item.dataKey as keyof typeof enumKey].color }}></div>
                                <div className="text-[16px]">{enumKey[item.dataKey as keyof typeof enumKey].value}</div>
                              </div>
                            );
                          })}
                        </div>
                      </React.Fragment>
                    );
                  }}
                />
                <Bar dataKey="y1" stackId="a" fill="#00E7C1" />
                <Bar dataKey="y2" stackId="a" fill="#E0C37D" />
                <Bar dataKey="y3" stackId="a" fill="#34E4FB" />
              </BarChart>
            </div>
          </div>
        </div>
      </Card>
    </React.Fragment>
  );
}

export default ConnectionStatus;
