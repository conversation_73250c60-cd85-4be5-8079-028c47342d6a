import { getFanShapedSurface } from "@/utils";
import { useMemo } from "react";

export interface IProps {
    data: {
        name: string;
        value: number;
        color: string;
    }[];
    activeIndex: number;
    activeHeight: number;
    externalRadius: number;
    ladderHeight: (value: number) => number;
}

function usePieOptions(props: IProps) {
    const { data, activeIndex, activeHeight, externalRadius, ladderHeight } = props;

    const sumValue = useMemo(() => {
        return data.reduce((accumulator, currentValue) => accumulator + currentValue.value, 0);
    }, [data]);

    const series = useMemo(() => {
        return data.map((item, index) => {
            const start =
                index > 0
                    ? data
                        .slice(0, index)
                        .reduce(
                            (accumulator, currentValue) => accumulator + currentValue.value,
                            0
                        )
                    : 0;
            const end = start + item.value;
            return {
                name: item.name,
                type: "surface",
                parametric: true,
                wireframe: {
                    show: false,
                },
                parametricEquation: getFanShapedSurface({
                    startRatio: start / sumValue,
                    endRatio: end / sumValue,
                    isSelected: false,
                    isHovered: false,
                    k: externalRadius,
                    h: index === activeIndex ? activeHeight : ladderHeight(item.value),
                }),
                itemStyle: {
                    color: item.color,
                    opacity: 0.95,
                },
            };
        });
    }, [activeHeight, activeIndex, data, externalRadius, ladderHeight, sumValue]);

    return {
        grid: {
            left: 20,
            right: 20,
            top: 20,
            bottom: 20,
        },
        xAxis3D: { min: -1, max: 1 },
        yAxis3D: { min: -1, max: 1 },
        zAxis3D: { min: -1, max: 1 },
        grid3D: {
            show: false,
            boxWidth: 200,
            boxHeight: 0.2,
            viewControl: {
                projection: "orthographic",
                alpha: 70,
                beta: 0,
                center: [0, 0, 0],
                rotateSensitivity: 0,
                zoomSensitivity: 0,
                distance: 1000,
            },
        },
        series: series,
    };
}

export default usePieOptions;