import Card from "@/components/Card";
import React, { useState } from "react";
import { <PERSON>, Pie, PieChart } from "recharts";

function CurrentAlarm() {
  const [alarmData, setAlarmData] = useState<any[]>([
    {
      label: "任务告警",
      value: 1,
    },
    {
      label: "性能告警",
      value: 0,
    },
    {
      label: "链路告警",
      value: 0,
    },
  ]);

  const alarmDataRadio = [
    {
      label: "紧急告警",
      value: 1,
      color: ["#2C51FA", "rgba(44,81,250,0.65)"],
    },
    // {
    //   label: "重要告警",
    //   value: 25,
    //   color: ["#A6CFFF", "rgba(166,207,255,0.75)"],
    // },
    {
      label: "重要告警",
      value: 1,
      color: ["#61B8FF", "rgba(97,184,255,0.75)"],
    },
    {
      label: "一般告警",
      value: 1,
      color: ["#FFCF5F", "rgba(255,207,95,0.75)"],
    },
    {
      label: "信息提示",
      value: 1,
      color: ["#EE6F7C", "rgba(238,111,124,0.75)"],
    },
  ];

  const alarmDataSum = alarmDataRadio.reduce(
    (acc, item) => acc + item.value,
    0
  );
  return (
    <React.Fragment>
      <Card
        title="当前告警"
        className="absolute w-[1336px] right-[100px] bottom-[86px]"
      >
        <div className="h-[460px]">
          <div className="size-full p-[24px_40px_28px_40px] flex flex-col gap-[32px]">
            <div className="flex items-center justify-between">
              {alarmData.map((item) => (
                <div
                  key={item.label}
                  className="h-[80px] flex items-center justify-center bg-[#445B77] px-[68px]"
                >
                  <span className="text-[26px] text-[#fff]">
                    {item.label}：
                  </span>
                  <span className="text-[42px] text-[#FF6200] pr-[4px]">
                    {item.value}
                  </span>
                  <span className="text-[24px] text-[#fff]">个</span>
                </div>
              ))}
            </div>
            <div className="flex-1 flex justify-between">
              <div className="w-[670px] bg-[#445B77] flex flex-col">
                <div className="h-[54px] flex gap-[16px] px-[16px] items-center">
                  <div className="size-[16px] bg-[#FF6200]"></div>
                  <div className="text-[26px]">告警等级占比</div>
                </div>
                <div className="flex gap-[52px] px-[60px]">
                  <div className="size-[208px]">
                    <PieChart width={208} height={208}>
                      {alarmDataRadio.map((item) => (
                        <defs key={`${item.label}-gradient`}>
                          <linearGradient
                            id={`${item.label}-gradient`}
                            x1="0"
                            y1="0"
                            x2="1"
                            y2="1"
                          >
                            <stop offset="0%" stopColor={item.color[0]} />
                            <stop offset="100%" stopColor={item.color[1]} />
                          </linearGradient>
                        </defs>
                      ))}
                      <Pie
                        data={alarmDataRadio}
                        innerRadius={75}
                        outerRadius={100}
                        paddingAngle={1}
                        dataKey="value"
                        startAngle={90}
                        endAngle={450}
                      >
                        {alarmDataRadio.map((entry) => (
                          <Cell
                            // 去除外边框
                            stroke="none"
                            key={`cell-${entry.label}`}
                            fill={`url(#${entry.label}-gradient)`}
                          />
                        ))}
                      </Pie>
                    </PieChart>
                  </div>
                  <div className="flex flex-col gap-[16px] justify-center">
                    {alarmDataRadio.map((item) => (
                      <div
                        key={item.label}
                        className="flex items-center gap-[16px]"
                      >
                        <div
                          className="size-[12px]"
                          style={{
                            background: `linear-gradient(to right, ${item.color[0]}, ${item.color[1]})`,
                          }}
                        ></div>
                        <div className="w-[128px] text-[24px]">
                          {item.label}
                        </div>
                        <div className="text-[24px]">{item.value}</div>
                        <div className="text-[24px]">
                          {(item.value / alarmDataSum) * 100}%
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
              <div className="w-[546px] bg-[#445B77]">
                <div className="h-[54px] flex gap-[16px] px-[16px] items-center">
                  <div className="size-[16px] bg-[#FF6200]"></div>
                  <div className="text-[26px]">今日告警处理</div>
                </div>
                <div className="flex justify-center items-center relative">
                  <PieChart width={208} height={208}>
                    <Pie
                      data={[{ value: 100 }]}
                      innerRadius={75}
                      outerRadius={100}
                      paddingAngle={1}
                      dataKey="value"
                      startAngle={90}
                      endAngle={450}
                    >
                      <Cell
                        // 去除外边框
                        stroke="none"
                        key={`cell-1`}
                        fill="#27C840"
                      />
                    </Pie>
                  </PieChart>
                  <div className="text-[24px] absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
                    100%
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </React.Fragment>
  );
}

export default CurrentAlarm;
