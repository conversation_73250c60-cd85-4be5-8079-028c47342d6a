import { memo, useMemo } from "react";
import StatisticalItem from "./StatisticalItem";
import { Tabs } from "antd";
import Card from "@/components/Card";
import { onFormatSplit } from "@/utils";

function NetworkSituation() {
  const columns = useMemo(() => {
    return [
      {
        title: "连接数",
        unit: "个",
        value: 3123234,
        needFormat: true,
        icon: "icon1",
      },
      {
        title: "调用量",
        unit: "个",
        value: 1234123,
        needFormat: true,
        icon: "icon2",
      },
      {
        title: "保障总时长",
        unit: "小时",
        value: 86421,
        needFormat: true,
        icon: "icon3",
      },
      {
        title: "系统并发数",
        unit: "tps",
        value: 5695,
        icon: "icon4",
      },
    ];
  }, []);

  return (
    <Card
      title="网络情况"
      className="absolute w-[1336px] left-[100px] top-[280px]"
      extra={
        <Tabs
          items={[
            {
              key: "1",
              label: "总数",
            },
            {
              key: "2",
              label: "用户优先级调度",
            },
            {
              key: "3",
              label: "业务带宽保障",
            },
            {
              key: "4",
              label: "用户动态限速",
            },
          ]}
        />
      }
    >
      <div className="h-[490px] px-[60px] py-[48px]">
        <div className="grid grid-cols-2 gap-x-[35px] gap-y-[33px]">
          {columns.map((val, index) => (
            <StatisticalItem
              key={`situation-${index}`}
              value={val.needFormat ? onFormatSplit(val.value) : val.value}
              title={val.title}
              unit={val.unit}
              icon={val.icon}
            />
          ))}
        </div>
      </div>
    </Card>
  );
}
export default memo(NetworkSituation);
