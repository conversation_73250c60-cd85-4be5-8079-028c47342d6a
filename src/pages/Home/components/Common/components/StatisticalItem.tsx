import React from "react";
import IconFont from "@/components/IconFont";

function DecorativeLine() {
  return (
    <div className="h-[2px] relative bg-[rgba(255,255,255,0.35)]">
      <span className="absolute w-[16px] h-full left-0 bg-[rgba(255,255,255,0.35)]"></span>
      <span className="absolute w-[16px] h-full right-0 bg-[rgba(255,255,255,0.35)]"></span>
    </div>
  );
}

interface IProps {
  icon?: string;
  title: string;
  value: React.ReactNode;
  /**
   * 单位
   */
  unit: string;
}
function StatisticalItem(props: IProps) {
  const { icon, title, value, unit } = props;

  return (
    <div>
      <DecorativeLine />
      <div className="flex h-[160px] gap-x-[12px] my-3">
        <div className="rounded-[32px_0_0_32px] w-[165px] bg-[#3F536F] flex items-center justify-center">
          <IconFont
            type={`icon-${icon}`}
            className="text-[80px] !text-[#5FEAFF]"
          />
        </div>
        <div className="rounded-[0_32px_32px_0] h-full px-8 flex-1 flex flex-col justify-center bg-[#3F536F]">
          <div className="text-[40px] pt-3 pb-2">{title}</div>
          <div className="flex items-baseline">
            <span className="text-[#5FEAFF] value-font text-[52px] leading-[60px]">
              {value}
            </span>
            <span className="text-[26px] text-white ml-4">{unit}</span>
          </div>
        </div>
      </div>
      <DecorativeLine />
    </div>
  );
}
export default StatisticalItem;
