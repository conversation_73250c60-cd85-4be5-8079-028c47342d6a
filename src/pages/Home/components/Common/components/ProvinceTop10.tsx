import Card from "@/components/Card";
import { onFormatSplit } from "@/utils";
import { useHover, useInterval } from "ahooks";
import clsx from "clsx";
import { motion } from "framer-motion";
import { useState, useRef } from "react";

function ProvinceTop10() {
  const data: Record<string, any>[] = [
    {
      rank: 1,
      province: "河北省",
      count: 620323,
    },
    {
      rank: 2,
      province: "上海",
      count: 457356,
    },
    {
      rank: 3,
      province: "浙江省",
      count: 61765,
    },
    {
      rank: 4,
      province: "海南省",
      count: 34234,
    },
    {
      rank: 5,
      province: "广东省",
      count: 23423,
    },
    {
      rank: 6,
      province: "山东省",
      count: 21423,
    },
    {
      rank: 7,
      province: "江苏省",
      count: 19876,
    },
    {
      rank: 8,
      province: "四川省",
      count: 18234,
    },
    {
      rank: 9,
      province: "湖南省",
      count: 16543,
    },
    {
      rank: 10,
      province: "福建省",
      count: 15432,
    },
  ];

  const domRef = useRef<HTMLDivElement>(null);

  const hovered = useHover(domRef);

  const [currentIndex, setCurrentIndex] = useState(0);

  const itemsPerPage = 5;

  const delay = 5000; // 滚动间隔，可以调整

  useInterval(
    () => {
      setCurrentIndex((prevIndex) => {
        // 当到达最后一组时，重新开始
        if (prevIndex >= data.length - itemsPerPage) {
          return 0;
        }
        return prevIndex + 1;
      });
    },
    hovered ? undefined : delay
  );

  const columns = [
    {
      title: "排名",
      dataIndex: "rank",
      render: (_: any, item: any) => (
        <div
          className={clsx("centered", {
            "rounded-full bg-[#5FEAFF] size-[40px] flex items-center justify-center":
              item.rank <= 3,
          })}
        >
          {item.rank}
        </div>
      ),
    },
    {
      title: "省份",
      dataIndex: "province",
    },
    {
      title: "连接数",
      dataIndex: "count",
      render: (value: number) => onFormatSplit(value),
    },
  ];

  return (
    <Card
      title="昨日分省连接Top10"
      className="absolute w-[1336px] left-[100px] bottom-[86px]"
    >
      <div className="h-[460px] space-y-3 px-[68px]" ref={domRef}>
        <div className="grid grid-cols-[172px_334px_480px] text-[26px] text-center h-[60px] leading-[60px] pt-2">
          {columns.map((item) => (
            <div className="truncate" key={item.dataIndex}>
              {item.title}
            </div>
          ))}
        </div>
        <div className="relative overflow-hidden h-[358px]">
          <motion.div
            animate={{ y: -currentIndex * 74 }} // 74px = 62px行高 + 3*4px间距
            transition={{
              duration: 0.6,
              ease: "easeInOut",
            }}
            className="space-y-3"
          >
            {data.map((val, index) => (
              <motion.div
                key={`data-${val.rank}`}
                className={clsx(
                  "grid grid-cols-[172px_334px_480px] text-[26px] text-center h-[62px] leading-[62px]",
                  {
                    "bg-[#3B506D]": index % 2 === 0,
                  }
                )}
                initial={{ opacity: 0, x: -20 }}
                animate={{
                  opacity:
                    index >= currentIndex && index < currentIndex + itemsPerPage
                      ? 1
                      : 0.3,
                  x: 0,
                }}
                transition={{
                  duration: 0.3,
                  delay: 0.1,
                }}
              >
                {columns.map((item) => (
                  <div className="truncate relative" key={item.dataIndex}>
                    {item.render?.(val?.[item.dataIndex], val) ??
                      val?.[item.dataIndex]}
                  </div>
                ))}
              </motion.div>
            ))}
          </motion.div>
        </div>
      </div>
    </Card>
  );
}
export default ProvinceTop10;
