import { memo, useMemo } from "react";
import StatisticalItem from "./StatisticalItem";
import Card from "@/components/Card";

function NetworkQuality() {
  const columns = useMemo(() => {
    return [
      {
        title: "业务成功率",
        unit: "%",
        value: 96.5,
        icon: "icon5",
      },
      {
        title: "调用量",
        unit: "ms",
        value: 45,
        icon: "icon6",
      },
      {
        title: "丢包率",
        unit: "%",
        value: 0.78,
        icon: "icon7",
      },
      {
        title: "业务连续性",
        unit: "%",
        value: 90.5,
        icon: "icon8",
      },
    ];
  }, []);

  return (
    <Card
      title="网络连接质量"
      className="absolute w-[1336px] left-[100px] top-[888px]"
    >
      <div className="h-[490px] px-[60px] py-[48px]">
        <div className="grid grid-cols-2 gap-x-[35px] gap-y-[33px]">
          {columns.map((val, index) => (
            <StatisticalItem
              key={`situation-${index}`}
              value={val.value}
              title={val.title}
              unit={val.unit}
              icon={val.icon}
            />
          ))}
        </div>
      </div>
    </Card>
  );
}
export default memo(NetworkQuality);
