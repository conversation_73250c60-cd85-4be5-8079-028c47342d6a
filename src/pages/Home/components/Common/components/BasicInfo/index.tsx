import type { TableProps } from "antd";
import { ConfigProvider, Table } from "antd";
import React from "react";
import bgIcon from "@/assets/images/headerBg.png";
import BasicInfoComp from "./components/BasicInfo";
import SceneInfoComp from "./components/SceneInfo";
import { CloseOutlined } from "@ant-design/icons";
import useStore from "@/store";
import { When } from "react-if";
import UserInfo from "./components/UserInfo";
import useModalVisible from "@/hooks/useModalVisible";

function BasicInfo() {
  const selectSubwayId = useStore((state) => state.selectSubwayId);

  const { visible, action } = useModalVisible();

  const columns: TableProps["columns"] = [
    {
      title: "手机号",
      dataIndex: "phone",
      key: "phone",
      render: (text: string) => {
        return (
          <div
            className="text-[42px] text-[#5FEAFF] cursor-pointer"
            onClick={() => {
              action.setTrue(text);
            }}
          >
            {text}
          </div>
        );
      },
    },
    {
      title: "优先级",
      dataIndex: "priority",
      key: "priority",
    },
    {
      title: "生效时长",
      dataIndex: "effectiveTime",
      key: "effectiveTime",
    },
    {
      title: "时延",
      dataIndex: "delay",
      key: "delay",
    },
  ];

  const data = Array.from({ length: 100 }, (_, index) => ({
    phone: `138****800${index}`,
    priority: 600,
    effectiveTime: `${Math.floor(Math.random() * 12) + 12}分钟`,
    delay: `${Math.floor(Math.random() * 10) + 10}ms`,
  }));

  const onHandleClose = () => {
    action.setFalse();
  };

  return (
    <React.Fragment>
      <When condition={selectSubwayId}>
        <div className="absolute h-[1793px] right-[2772px] top-[280px] pointer-events-auto z-[21]">
          <div className="size-full flex gap-[24px] justify-end">
            <div className="w-[1328px] h-[1793px] bg-[#2A4160] pt-[45px] flex flex-col">
              <div
                className="h-[80px] relative"
                style={{
                  background: `url(${bgIcon}) no-repeat`,
                  backgroundSize: "cover",
                  backgroundPosition: "center",
                }}
              >
                <div
                  className="text-[42px] absolute left-[130px] top-[0px]"
                  style={{
                    background:
                      "linear-gradient(90deg, #DFF0FA 19%, #E0F0FF 46%, #A8D4F9 82%);",
                  }}
                >
                  地铁1号线用户信息
                </div>
              </div>
              <div className="flex w-[1325px] h-[1500px] px-[32px]">
                <ConfigProvider
                  theme={{
                    components: {
                      Table: {
                        headerBg: "#354A63",
                        fontSize: 42,
                        colorBgContainer: "#2A4160",
                      },
                      Pagination: {
                        itemActiveBg: "#165184",
                        colorPrimary: "#3678BC",
                      },
                    },
                  }}
                >
                  <Table
                    className="w-full h-full"
                    columns={columns}
                    dataSource={data}
                    pagination={{
                      pageSize: 15,
                      showSizeChanger: false,
                    }}
                  />
                </ConfigProvider>
              </div>
            </div>
            <div className="w-[1408px] h-[1793px] bg-[#2A4160] px-[56px] py-[52px] flex flex-col relative">
              <div className="absolute right-[50px] top-[50px]">
                <CloseOutlined
                  className="text-[42px] text-[#fff] cursor-pointer"
                  onClick={onHandleClose}
                />
              </div>
              <BasicInfoComp />
              <SceneInfoComp />
            </div>
          </div>
        </div>
        <UserInfo open={visible} onCancel={onHandleClose} />
      </When>
    </React.Fragment>
  );
}

export default BasicInfo;
