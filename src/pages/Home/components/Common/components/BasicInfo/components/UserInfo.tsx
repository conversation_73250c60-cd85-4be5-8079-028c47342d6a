import type { ModalProps } from "antd";
import { ConfigProvider, Form, Input, Modal, Radio } from "antd";
import React from "react";

interface IProps extends ModalProps {
  selectItem: any;
}

function UserInfo(props: IProps) {
  const { open, onCancel, selectItem } = props;
  return (
    <React.Fragment>
      <ConfigProvider
        theme={{
          components: {
            Modal: {
              contentBg: "#233954",
              headerBg: "#233954",
            },
          },
        }}
      >
        <Modal
          title="用户信息"
          closable={{ "aria-label": "Custom Close Button" }}
          open={open}
          onCancel={onCancel}
        >
          <Form
            labelCol={{ span: 4 }}
            wrapperCol={{ span: 20 }}
            style={{ maxWidth: 600 }}
            initialValues={{ remember: true }}
            autoComplete="off"
          >
            <Form.Item label="手机号" name="phone" rules={[{ required: true }]}>
              <Input />
            </Form.Item>
            <Form.Item
              label="连接时长"
              name="time"
              rules={[{ required: true }]}
            >
              <div className="flex justify-between">
                <div>12分钟</div>
                <div>开始时间：2025-08-01 12:12</div>
                <div>限速速率：20kbps</div>
              </div>
            </Form.Item>
            <Form.Item
              label="连接类型"
              name="type"
              rules={[{ required: true }]}
            >
              <Radio.Group
                style={{
                  display: "flex",
                  flexDirection: "column",
                  gap: 8,
                  marginTop:"6px"
                }}
                options={[
                  { value: 1, label: "1kbps限速" },
                  { value: 2, label: "20kbps限速" },
                  { value: 3, label: "128kbps限速-1Mkbps限速（冰激凌）" },
                  { value: 4, label: "1Mkbps限速-2Mkbps限速（冰激凌）" },
                  { value: 5, label: "2Mkbps限速-7.2Mkbps限速（冰激凌）" },
                  { value: 6, label: "解除限速" },
                ]}
              />
            </Form.Item>
          </Form>
        </Modal>
      </ConfigProvider>
    </React.Fragment>
  );
}

export default UserInfo;
