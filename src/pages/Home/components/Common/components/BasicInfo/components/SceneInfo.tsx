import React, { useMemo } from "react";
import bgIcon from "@/assets/images/headerBg.png";
import {
    Area,
    AreaChart,
    CartesianGrid,
    Tooltip,
    XAxis,
    YAxis,
  } from "recharts";

function SceneInfo() {
  const sceneInfo = [
    {
      x1: "400",
      x2: "4个",
      x3: "1345",
      x4: "2280",
    },
    {
      x1: "600",
      x2: "1个",
      x3: "666",
      x4: "1145",
    },
    {
      x1: "800",
      x2: "1个",
      x3: "300",
      x4: "654",
    },
    {
      x1: "800-1000",
      x2: "2个",
      x3: "679",
      x4: "324",
    },
    {
      x1: "1000以上",
      x2: "1个",
      x3: "246",
      x4: "312",
    },
  ];

  const serviceClassify = useMemo(() => {
    const y1 = [790, 590, 800, 590, 800, 580, 700, 790];
    const y2 = [600, 490, 620, 290, 290, 370, 430, 500];
    const y3 = [520, 180, 420, 190, 210, 180, 300, 430];
    return Array.from({ length: 8 }, (_, index) => ({
      x: `${(index + 7).toString()}:00`,
      y1: y1[index],
      y2: y2[index],
      y3: y3[index],
    }));
  }, []);

  const colorList = [
    ["#01CB79", "rgba(1,203,121,0.2)"],
    ["#59E1F7", "rgba(89,225,247,0.2)"],
    ["#01CB79", "rgba(1,203,121,0.2)"],
  ];
  return (
    <React.Fragment>
      <div className="flex flex-col mt-[75px]">
        <div
          className="h-[80px] relative"
          style={{
            background: `url(${bgIcon}) no-repeat`,
            backgroundSize: "cover",
            backgroundPosition: "center",
          }}
        >
          <div
            className="text-[42px] absolute left-[130px] top-[0px]"
            style={{
              background:
                "linear-gradient(90deg, #DFF0FA 19%, #E0F0FF 46%, #A8D4F9 82%);",
            }}
          >
            场景信息
          </div>
        </div>
        <div className="mt-[36px]">
          <div className="grid h-[100px] items-center bg-[#354A63] grid-cols-[auto_160px_260px_260px] gap-[16px] text-[42px] px-[36px]">
            <div>优先级</div>
            <div>小区数</div>
            <div>连接数（个）</div>
            <div>用户（户）</div>
          </div>
          <div className="flex flex-col">
            {sceneInfo.map((item, index) => (
              <div
                key={index}
                className="grid h-[100px] items-center grid-cols-[auto_160px_260px_260px] gap-[16px] text-[42px] px-[36px] hover:bg-[#2A607B] border-b border-[#fff]"
              >
                <div>{item.x1}</div>
                <div>{item.x2}</div>
                <div>{item.x3}</div>
                <div>{item.x4}</div>
              </div>
            ))}
          </div>
        </div>
        <div className="flex flex-col mt-[46px]">
          <div
            className="h-[80px] relative"
            style={{
              background: `url(${bgIcon}) no-repeat`,
              backgroundSize: "cover",
              backgroundPosition: "center",
            }}
          >
            <div
              className="text-[42px] absolute left-[130px] top-[0px]"
              style={{
                background:
                  "linear-gradient(90deg, #DFF0FA 19%, #E0F0FF 46%, #A8D4F9 82%);",
              }}
            >
              效果数据
            </div>
          </div>
          <div className="h-[490px]">
            <AreaChart
              width={1300}
              height={490}
              data={serviceClassify}
              margin={{
                top: 80,
                right: 30,
                left: 0,
                bottom: 0,
              }}
            >
              {colorList.map((item, index) => (
                <defs key={index}>
                  <linearGradient
                    id={`colorUv${index}`}
                    x1="0"
                    y1="0"
                    x2="0"
                    y2="1"
                  >
                    <stop offset="5%" stopColor={item[0]} stopOpacity={0.8} />
                    <stop offset="95%" stopColor={item[1]} stopOpacity={0} />
                  </linearGradient>
                </defs>
              ))}
              <CartesianGrid strokeDasharray="3 3" vertical={false} />
              <XAxis dataKey="x" stroke="#fff" tickLine={false} fontSize={24} />
              <YAxis
                stroke="#fff"
                tickLine={false}
                fontSize={24}
                axisLine={false}
                label={{
                  value: "单位：Mbps",
                  color: "#fff",
                  position: "insideTop",
                  fontSize: 24,
                  fontWeight: 500,
                  fontFamily: "PingFang SC",
                  dx: 40,
                  dy: -60,
                  angle: 0,
                  fill: "#fff",
                }}
              />
              <Tooltip />
              {serviceClassify.map((item, index) => (
                <Area
                  key={index}
                  type="linear"
                  dataKey={`y${index + 1}`}
                  strokeWidth={4}
                  stroke={colorList?.[index]?.[0]}
                  fill={`url(#colorUv${index})`}
                />
              ))}
            </AreaChart>
          </div>
        </div>
      </div>
    </React.Fragment>
  );
}

export default SceneInfo;
