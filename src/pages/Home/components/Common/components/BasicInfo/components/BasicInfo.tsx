import bgIcon from "@/assets/images/headerBg.png";
import React from "react";

function BasicInfo() {
  return (
    <React.Fragment>
      <div
        className="h-[80px] relative"
        style={{
          background: `url(${bgIcon}) no-repeat`,
          backgroundSize: "cover",
          backgroundPosition: "center",
        }}
      >
        <div
          className="text-[42px] absolute left-[130px] top-[0px]"
          style={{
            background:
              "linear-gradient(90deg, #DFF0FA 19%, #E0F0FF 46%, #A8D4F9 82%);",
          }}
        >
          基础信息
        </div>
      </div>
      <div className="px-[20px] mt-[32px] flex justify-between">
        <div className="w-[392px] h-[192px] bg-gradient-to-r from-[rgba(255,255,255,0.1)] to-[rgba(255,255,255,0)] border-[#5FEAFF] border-l-[6px] p-[32px]">
          <div className="h-[64px] text-[#5CDFEE] text-[48px] flex items-center">
            区域
          </div>
          <div className="text-[48px] text-[#fff] h-[64px] flex items-center">
            1号线
          </div>
        </div>
        <div className="w-[392px] h-[192px] bg-gradient-to-r from-[rgba(255,255,255,0.1)] to-[rgba(255,255,255,0)] border-[#5FEAFF] border-l-[6px] p-[32px]">
          <div className="h-[64px] text-[#5CDFEE] text-[48px] flex items-center">
            无线小区数
          </div>
          <div className="text-[48px] text-[#fff] h-[64px] flex items-center">
            9<span className="text-[#84E6FB] text-[26px] ml-[16px]">个</span>
          </div>
        </div>
        <div className="w-[392px] h-[192px] bg-gradient-to-r from-[rgba(255,255,255,0.1)] to-[rgba(255,255,255,0)] border-[#5FEAFF] border-l-[6px] p-[32px]">
          <div className="h-[64px] text-[#5CDFEE] text-[48px] flex items-center">
            用户数
          </div>
          <div className="text-[48px] text-[#fff] h-[64px] flex items-center">
            4643
            <span className="text-[#84E6FB] text-[26px] ml-[16px]">户</span>
          </div>
        </div>
      </div>
    </React.Fragment>
  );
}

export default BasicInfo;
