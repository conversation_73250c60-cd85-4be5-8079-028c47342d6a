import Card from "@/components/Card";
import React, { useMemo, useState } from "react";
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
} from "recharts";

function ConditionService() {
  const [serviceKey, setServiceKey] = useState("user");

  const data = [
    280,240,200,200,210,230,240,240,240,240,
    240,250,240,230,240,240,240,250,260,270,
    280,280,280,280
  ]

  const serviceClassify = useMemo(() => {
    return [
      {
        name: "用户优先级调度",
        key: "user",
        // 00:00 到 24：00的假数据
        value: Array.from({ length: 24 }, (_, index) => ({
          // 时间格式化为 HH:00，index从0到23对应0点到23点
          x: `${index.toString().padStart(2, "0")}:00`,
          y: data[index],
        })),
      },
      {
        name: "业务带宽保障",
        key: "business",
        value: Array.from({ length: 24 }, (_, index) => ({
          // 时间格式化为 HH:00，index从0到23对应0点到23点
          x: `${index.toString().padStart(2, "0")}:00`,
          y: (Math.random() * 100).toFixed(2),
        })),
      },
      {
        name: "用户动态限速",
        key: "dynamic",
        value: Array.from({ length: 24 }, (_, index) => ({
          // 时间格式化为 HH:00，index从0到23对应0点到23点
          x: `${index.toString().padStart(2, "0")}:00`,
          y: (Math.random() * 100).toFixed(2),
        })),
      },
    ];
  }, []);

  const serviceData = useMemo(() => {
    return serviceClassify.find((item) => item.key === serviceKey)?.value;
  }, [serviceClassify, serviceKey]);

  return (
    <React.Fragment>
      <Card
        title="连接使用情况"
        className="absolute w-[1336px] right-[100px] top-[888px]"
        extra={
          <div className="flex items-center gap-[40px]">
            {serviceClassify.map((item) => (
              <div
                key={item.key}
                className="flex items-center relative h-[68px] cursor-pointer"
                onClick={() => setServiceKey(item.key)}
              >
                <span
                  className="text-[24px] leading-[36px]"
                  style={{
                    color: item.key === serviceKey ? "#5CDFEE" : "#94AABD",
                  }}
                >
                  {item.name}
                </span>
                {item.key === serviceKey && (
                  <div className="absolute left-0 bottom-0 w-full h-[2px] bg-[#5CDFEE]"></div>
                )}
              </div>
            ))}
          </div>
        }
      >
        <div className="h-[490px]">
          <div className="size-full">
            <AreaChart
              width={1280}
              height={450}
              data={serviceData}
              margin={{
                top: 70,
                right: 20,
                left: 70,
                bottom: 14,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" vertical={false} color="#556780" />
              <XAxis dataKey="x" stroke="#fff" tickLine={false} fontSize={24} dy={16} />
              {/* 指定数据位0,100,200,300，400 */}
              <YAxis
                ticks={[0, 100, 200, 300, 400]}
                stroke="#fff"
                tickLine={false}
                fontSize={24}
                axisLine={false}
                label={{
                  value: "单位:万户",
                  color: "#fff",
                  position: "insideTop",
                  fontSize: 22,
                  fontWeight: 500,
                  fontFamily: "PingFang SC",
                  dx: 10,
                  dy: -50,
                  angle: 0,
                  fill: "#fff",
                }}
              />
              {/* <Tooltip /> */}
              <defs>
                <linearGradient id="colorUv" x1="0" y1="0" x2="0" y2="1">
                  <stop
                    offset="5%"
                    stopColor="rgba(95,234,255,0.45)"
                    stopOpacity={0.8}
                  />
                  <stop
                    offset="95%"
                    stopColor="rgba(95,234,255,0)"
                    stopOpacity={0}
                  />
                </linearGradient>
              </defs>
              <Area
                type="monotone"
                dataKey="y"
                stroke="#3FE7FC"
                strokeWidth={4}
                fill="url(#colorUv)"
              />
            </AreaChart>
          </div>
        </div>
      </Card>
    </React.Fragment>
  );
}
export default ConditionService;
