import useStore from "@/store";
import React, { memo } from "react";
import { When } from "react-if";
import SubwayInfo from "./components/SubwayInfo";
// import ResidenceInfo from "./components/ResidenceInfo";
// import TrafficInfo from "./components/TrafficInfo";

function DetailInfo() {
  const selectSubwayId = useStore((state) => state.selectSubwayId);

  return (
    <React.Fragment>
      <When condition={selectSubwayId}>
        <SubwayInfo />
      </When>
      {/* <ResidenceInfo /> */}
      {/* <TrafficInfo/> */}
    </React.Fragment>
  );
}
export default memo(DetailInfo);
