import SubTitle from "@/components/SubTitle";
import type { UserInfo } from "../..";
import useStore from "@/store";
import type { TableProps, FormInstance } from "antd";
import { Button, Form, InputNumber, Modal, Radio, Table } from "antd";
import { CloseOutlined } from "@ant-design/icons";
import { randomInt } from "es-toolkit";
import React, { useRef, useState } from "react";

interface DataItem {
  /**
   * 手机号
   */
  phone: string;

  /**
   * 优先级
   */
  priority: string;

  /**
   * 生效时长
   */
  effectiveTime: string;

  /**
   * 时延
   */
  delay: string;

  /**
   * id
   */
  id: string;
}

interface IProps {
  selectItem: UserInfo;

  setSelectItem: (item?: UserInfo) => void;
}
function UserList(props: IProps) {
  const { selectItem, setSelectItem } = props;

  const selectSubwayId = useStore((state) => state.selectSubwayId);

  const [selectDataItem, setSelectDataItem] = useState<DataItem>();

  const modalDomRef = useRef<HTMLDivElement>(null);

  const columns: TableProps<DataItem>["columns"] = [
    {
      title: "手机号",
      dataIndex: "phone",
      key: "phone",
      width: 400,
      render: (text: string, record) => {
        return (
          <div
            className="text-[42px] text-[#5FEAFF] cursor-pointer"
            onClick={() => {
              setSelectDataItem(record);
            }}
          >
            {text}
          </div>
        );
      },
    },
    {
      title: "优先级",
      dataIndex: "priority",
      key: "priority",
    },
    {
      title: "生效时长",
      dataIndex: "effectiveTime",
      key: "effectiveTime",
    },
    {
      title: "时延",
      dataIndex: "delay",
      key: "delay",
    },
  ];

  const onInitData = () => {
    const value = Array.from({ length: 100 }, (_, index) => {
      let priority = selectItem.priority;
      if (priority === "800-1000") {
        priority = randomInt(800, 1000).toString();
      } else if (priority === "1000以上") {
        priority = randomInt(1000, 2000).toString();
      }
      return {
        phone: `138****800${index}`,
        priority,
        effectiveTime: `${Math.floor(Math.random() * 12) + 12}分钟`,
        delay: `${Math.floor(Math.random() * 10) + 10}ms`,
        id: index.toString(),
      };
    });
    return value;
  };

  const [data, setData] = useState(onInitData());
  const onClose = () => {
    setSelectItem(undefined);
  };

  const formRef = useRef<FormInstance<DataItem>>(null);

  const onSubmit = async () => {
    const values = await formRef.current?.validateFields();
    if (!values || !selectDataItem) return;

    setData((prevData) => {
      const newData = [...prevData];
      const index = newData.findIndex((item) => item.id === selectDataItem.id);
      if (index !== -1) {
        newData[index] = {
          ...selectDataItem,
          ...values,
        } as DataItem;
      }
      return newData;
    });
    setSelectDataItem(undefined);
  };

  return (
    <React.Fragment>
      <div className="absolute h-[1793px] w-[1328px] right-[4204px] top-[280px] pointer-events-auto z-[20] bg-[#2A4160] pt-[45px] shadow-[0px_6px_10px_0px_rgba(0,0,0,0.06)]">
        <div className="absolute right-[50px] top-[50px] z-10">
          <CloseOutlined
            className="text-[42px] text-[#fff] cursor-pointer"
            onClick={onClose}
          />
        </div>
        <SubTitle title={`地铁${selectSubwayId}用户信息`} />
        <div className="flex w-[1325px] h-[1500px] px-[45px] mt-[35px]">
          <Table
            className="w-full h-full"
            columns={columns}
            dataSource={data}
            pagination={{
              pageSize: 14,
              size: "default",
              showSizeChanger: false,
              style: {
                marginTop: 46,
                fontSize: 32,
              },
            }}
          />
        </div>
      </div>
      <div ref={modalDomRef}>
        <Modal
          title="用户信息"
          open={Boolean(selectDataItem)}
          onCancel={() => setSelectDataItem(undefined)}
          maskClosable={false}
          style={{
            borderRadius: 32,
            overflow: "hidden",
          }}
          styles={{
            header: {
              padding: "40px 36px",
            },
            body: {
              padding: "0 42px",
              overflow: "hidden",
              borderRadius: 32,
            },
          }}
          closable={false}
          centered
          width={1400}
          height={1060}
          footer={null}
          destroyOnHidden
          getContainer={() => modalDomRef.current!}
        >
          <div className="w-full h-[960px] pt-2">
            <Form
              labelCol={{ flex: "160px", style: { marginRight: 10 } }}
              wrapperCol={{ flex: "auto" }}
              initialValues={selectDataItem}
              ref={formRef}
              autoComplete="off"
            >
              <Form.Item label="手机号" name="phone">
                <InputNumber controls={false} className="!w-full" />
              </Form.Item>
              <Form.Item label="连接时长">
                <div className="flex justify-between text-[30px]">
                  <div>{selectDataItem?.effectiveTime}</div>
                  <div>平均速率：7.2Mbps</div>
                  <div>平均时延：{selectDataItem?.delay}</div>
                </div>
              </Form.Item>
              <Form.Item label="优先级" name="priority">
                <Radio.Group
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    gap: 32,
                    marginTop: 16,
                  }}
                  options={[
                    { value: "1000以上", label: "1000以上优先级" },
                    { value: "800-1000", label: "800-1000优先级" },
                    { value: "800", label: "800优先级" },
                    { value: "600", label: "600优先级" },
                    { value: "400", label: "400优先级" },
                  ]}
                />
              </Form.Item>
              <Form.Item label="&nbsp;" colon={false}>
                <div className="flex gap-x-[38px] mt-[42px]">
                  <Button
                    type="primary"
                    className="w-[140px] !h-[68px] !text-[30px] !rounded-[14px]"
                    onClick={onSubmit}
                  >
                    提交
                  </Button>
                  <Button
                    type="default"
                    ghost
                    className="w-[140px] !h-[68px] !text-[30px] !text-white !border-white !rounded-[14px]"
                    onClick={() => setSelectDataItem(undefined)}
                  >
                    取消
                  </Button>
                </div>
              </Form.Item>
            </Form>
          </div>
        </Modal>
      </div>
    </React.Fragment>
  );
}
export default UserList;
