import SubTitle from "@/components/SubTitle";
import useStore from "@/store";
import { CloseOutlined } from "@ant-design/icons";
import React, { memo, useState } from "react";
import clsx from "clsx";
import UserList from "./components/UserList";
import type { TableProps } from "antd";
import { ConfigProvider, Table } from "antd";

export interface UserInfo {
  /**
   * 优先级
   */
  priority: string;

  /**
   * 小区数
   */
  communityCount: string;

  /**
   * 连接数
   */
  connectionCount: number;

  /**
   * 用户
   */
  userCount: number;

  /**
   * id
   */
  id: number;
}

interface DataItem {
  /**
   * 手机号
   */
  phone: string;

  /**
   * 连接类型
   */
  connectionType: string;

  /**
   * 生效时长
   */
  effectiveDuration: string;

  /**
   * 开始时间
   */
  startTime: string;

  /**
   * id
   */
  id: number;
}

function ResidenceInfo() {
  const selectSubwayId = useStore((state) => state.selectSubwayId);

  const [selectItem, setSelectItem] = useState<UserInfo>();

  const [selectDataItem, setSelectDataItem] = useState<DataItem>();

  const onClose = () => {
    useStore.setState((draft) => {
      draft.selectSubwayId = undefined;
    });
  };

  const sceneList: UserInfo[] = [
    {
      priority: "1kbps限速",
      communityCount: "1kbps",
      connectionCount: 223,
      userCount: 123,
      id: 1,
    },
    {
      priority: "20kbps限速",
      communityCount: "20kbps",
      connectionCount: 100,
      userCount: 94,
      id: 2,
    },
    {
      priority: "128kbps-1Mkbps限速",
      communityCount: "128kbps-1Mkbps",
      connectionCount: 65,
      userCount: 76,
      id: 3,
    },
    {
      priority: "1-2Mkbps限速",
      communityCount: "1-2Mkbps",
      connectionCount: 45,
      userCount: 65,
      id: 4,
    },
    {
      priority: "2-7.2Mkbps",
      communityCount: "2-7.2Mkbps",
      connectionCount: 34,
      userCount: 45,
      id: 5,
    },
  ];

  const infoList = [
    {
      phone: "18687827722",
      connectionType: "20kbps限速",
      effectiveDuration: "12分钟",
      startTime: "2025-08-01 12:12",
      id: 1,
    },
    {
      phone: "18687821984",
      connectionType: "20kbps限速",
      effectiveDuration: "34分钟",
      startTime: "2025-08-04 13:12",
      id: 2,
    },
    {
      phone: "18687820016",
      connectionType: "20kbps限速",
      effectiveDuration: "23分钟",
      startTime: "2025-08-12 12:34",
      id: 3,
    },
    {
      phone: "18687827678",
      connectionType: "20kbps限速",
      effectiveDuration: "12分钟",
      startTime: "2025-08-01 12:12",
      id: 4,
    },
  ];

  const columns: TableProps<DataItem>["columns"] = [
    {
      title: "手机号",
      dataIndex: "phone",
      key: "phone",
      width: 300,
      render: (text: string, record: DataItem) => {
        return (
          <div
            className="text-[42px] text-[#5FEAFF] cursor-pointer"
            onClick={() => {
              setSelectDataItem(record);
            }}
          >
            {text.slice(0, 3)}****{text.slice(7)}
          </div>
        );
      },
    },
    {
      title: "连接类型",
      dataIndex: "connectionType",
      key: "connectionType",
    },
    {
      title: "生效时长",
      dataIndex: "effectiveDuration",
      key: "effectiveDuration",
    },
    {
      title: "开始时间",
      dataIndex: "startTime",
      key: "startTime",
    },
  ];

  return (
    <React.Fragment>
      <ConfigProvider
        theme={{
          components: {
            Table: {
              headerBg: "#3F5470",
              fontSize: 33,
              colorBgContainer: "#2A4160",
              cellPaddingInline: 60,
              cellPaddingBlock: 16,
              borderColor: "rgba(255, 255, 255, 0.30)",
            },
          },
        }}
      >
        <div className="w-[1408px] h-[1793px] bg-[#2A4160] px-[56px] py-[52px] flex flex-col absolute right-[2772px] top-[280px] pointer-events-auto z-50 shadow-[0px_6px_10px_0px_rgba(0,0,0,0.06)]">
          <div className="absolute right-[50px] top-[50px] z-10">
            <CloseOutlined
              className="text-[42px] text-[#fff] cursor-pointer"
              onClick={onClose}
            />
          </div>
          <SubTitle title="基础信息" />
          <div className="px-[20px] mt-[32px] flex justify-between">
            <div className="w-[392px] h-[192px] bg-gradient-to-r from-[rgba(255,255,255,0.1)] to-[rgba(255,255,255,0)] border-[#5FEAFF] border-l-[6px] p-[32px]">
              <div className="h-[64px] text-[#5CDFEE] text-[48px] flex items-center mb-1">
                区域
              </div>
              <div className="text-[48px] text-[#fff] h-[64px] flex items-center">
                {selectSubwayId}
              </div>
            </div>
            <div className="w-[392px] h-[192px] bg-gradient-to-r from-[rgba(255,255,255,0.1)] to-[rgba(255,255,255,0)] border-[#5FEAFF] border-l-[6px] p-[32px]">
              <div className="h-[64px] text-[#5CDFEE] text-[48px] flex items-center mb-1">
                无线小区数
              </div>
              <div className="text-[48px] text-[#fff] h-[64px] flex items-center">
                8
                <span className="text-[#84E6FB] text-[26px] ml-[16px]">个</span>
              </div>
            </div>
            <div className="w-[392px] h-[192px] bg-gradient-to-r from-[rgba(255,255,255,0.1)] to-[rgba(255,255,255,0)] border-[#5FEAFF] border-l-[6px] p-[32px]">
              <div className="h-[64px] text-[#5CDFEE] text-[48px] flex items-center mb-1">
                用户数
              </div>
              <div className="text-[48px] text-[#fff] h-[64px] flex items-center">
                235
                <span className="text-[#84E6FB] text-[26px] ml-[16px]">户</span>
              </div>
            </div>
          </div>
          <SubTitle title="场景信息" className="mt-[75px]" />
          <div className="grid mt-[36px] min-h-[100px] items-center bg-[#354A63] grid-cols-[auto_300px_200px_200px] gap-[16px] text-[32px] px-[36px]">
            <div>连接类型</div>
            <div>限速带宽</div>
            <div>用户数（户）</div>
            <div>连接数（个）</div>
          </div>
          <div className="flex flex-col">
            {sceneList.map((item, index) => (
              <div
                key={index}
                className={clsx(
                  "grid h-[100px] items-center grid-cols-[auto_300px_200px_200px] gap-[16px] text-[32px] px-[36px] hover:bg-[rgba(255,255,255,0.1)] border-b cursor-pointer border-[#fff]",
                  {
                    "!bg-[rgba(95,234,255,0.25)]": selectItem?.id === item.id,
                  }
                )}
                onClick={() => setSelectItem(item)}
              >
                <div>{item.priority}</div>
                <div>{item.communityCount}</div>
                <div>{item.connectionCount}</div>
                <div>{item.userCount}</div>
              </div>
            ))}
          </div>
          <SubTitle title="用户信息" className="mt-[52px]" />

          <Table
            className="w-full h-full mt-[28px]"
            columns={columns as any}
            dataSource={infoList}
            pagination={{
              pageSize: 3,
              size: "default",
              showSizeChanger: false,
              style: {
                marginTop: 46,
                fontSize: 32,
              },
            }}
          />
        </div>
        {selectItem && (
          <UserList
            key={selectItem.id}
            selectItem={selectItem}
            setSelectItem={setSelectItem}
          />
        )}
      </ConfigProvider>
    </React.Fragment>
  );
}

export default memo(ResidenceInfo);
