import SubTitle from "@/components/SubTitle";
import useStore from "@/store";
import { CloseOutlined } from "@ant-design/icons";
import { Area, AreaChart, CartesianGrid, XAxis, YAxis } from "recharts";
import React, { memo, useMemo, useState } from "react";
import clsx from "clsx";
import UserList from "./components/UserList";

export interface UserInfo {
  /**
   * 优先级
   */
  priority: string;

  /**
   * 小区数
   */
  communityCount: string;

  /**
   * 连接数
   */
  connectionCount: number;

  /**
   * 用户
   */
  userCount: number;

  /**
   * id
   */
  id: number;
}

function SubwayInfo() {
  const selectSubwayId = useStore((state) => state.selectSubwayId);

  const [selectItem, setSelectItem] = useState<UserInfo>();

  const onClose = () => {
    useStore.setState((draft) => {
      draft.selectSubwayId = undefined;
    });
  };

  const sceneList: UserInfo[] = [
    {
      priority: "400",
      communityCount: "4个",
      connectionCount: 1345,
      userCount: 2280,
      id: 1,
    },
    {
      priority: "600",
      communityCount: "1个",
      connectionCount: 666,
      userCount: 1145,
      id: 2,
    },
    {
      priority: "800",
      communityCount: "1个",
      connectionCount: 300,
      userCount: 654,
      id: 3,
    },
    {
      priority: "800-1000",
      communityCount: "2个",
      connectionCount: 679,
      userCount: 324,
      id: 4,
    },
    {
      priority: "1000以上",
      communityCount: "1个",
      connectionCount: 246,
      userCount: 312,
      id: 5,
    },
  ];

  const serviceClassify = useMemo(() => {
    const y1 = [790, 590, 800, 590, 800, 580, 700, 790];
    const y2 = [600, 490, 620, 290, 290, 370, 430, 500];
    const y3 = [520, 180, 420, 190, 210, 180, 300, 430];
    return Array.from({ length: 8 }, (_, index) => ({
      x: `${(index + 7).toString()}:00`,
      y1: y1[index],
      y2: y2[index],
      y3: y3[index],
    }));
  }, []);

  const colorList = [
    ["#E0C37D", "rgba(224,195,125,0.2)"],
    ["#01CB79", "rgba(1,203,121,0.2)"],
    ["#59E1F7", "rgba(89,225,247,0.2)"],
  ];

  return (
    <React.Fragment>
      <div className="w-[1408px] h-[1793px] bg-[#2A4160] px-[56px] py-[52px] flex flex-col absolute right-[2772px] top-[280px] pointer-events-auto z-50 shadow-[0px_6px_10px_0px_rgba(0,0,0,0.06)]">
        <div className="absolute right-[50px] top-[50px] z-10">
          <CloseOutlined
            className="text-[42px] text-[#fff] cursor-pointer"
            onClick={onClose}
          />
        </div>
        <SubTitle title="基础信息" />
        <div className="px-[20px] mt-[32px] flex justify-between">
          <div className="w-[392px] h-[192px] bg-gradient-to-r from-[rgba(255,255,255,0.1)] to-[rgba(255,255,255,0)] border-[#5FEAFF] border-l-[6px] p-[32px]">
            <div className="h-[64px] text-[#5CDFEE] text-[48px] flex items-center mb-1">
              区域
            </div>
            <div className="text-[48px] text-[#fff] h-[64px] flex items-center">
              {selectSubwayId}
            </div>
          </div>
          <div className="w-[392px] h-[192px] bg-gradient-to-r from-[rgba(255,255,255,0.1)] to-[rgba(255,255,255,0)] border-[#5FEAFF] border-l-[6px] p-[32px]">
            <div className="h-[64px] text-[#5CDFEE] text-[48px] flex items-center mb-1">
              无线小区数
            </div>
            <div className="text-[48px] text-[#fff] h-[64px] flex items-center">
              9<span className="text-[#84E6FB] text-[26px] ml-[16px]">个</span>
            </div>
          </div>
          <div className="w-[392px] h-[192px] bg-gradient-to-r from-[rgba(255,255,255,0.1)] to-[rgba(255,255,255,0)] border-[#5FEAFF] border-l-[6px] p-[32px]">
            <div className="h-[64px] text-[#5CDFEE] text-[48px] flex items-center mb-1">
              用户数
            </div>
            <div className="text-[48px] text-[#fff] h-[64px] flex items-center">
              4643
              <span className="text-[#84E6FB] text-[26px] ml-[16px]">户</span>
            </div>
          </div>
        </div>
        <SubTitle title="场景信息" className="mt-[75px]" />
        <div className="grid mt-[36px] h-[100px] items-center bg-[#354A63] grid-cols-[auto_160px_260px_260px] gap-[16px] text-[42px] px-[36px]">
          <div>优先级</div>
          <div>小区数</div>
          <div>连接数（个）</div>
          <div>用户（户）</div>
        </div>
        <div className="flex flex-col">
          {sceneList.map((item, index) => (
            <div
              key={index}
              className={clsx(
                "grid h-[100px] items-center grid-cols-[auto_160px_260px_260px] gap-[16px] text-[42px] px-[36px] hover:bg-[rgba(255,255,255,0.1)] border-b cursor-pointer border-[#fff]",
                {
                  "!bg-[rgba(95,234,255,0.25)]": selectItem?.id === item.id,
                }
              )}
              onClick={() => setSelectItem(item)}
            >
              <div>{item.priority}</div>
              <div>{item.communityCount}</div>
              <div>{item.connectionCount}</div>
              <div>{item.userCount}</div>
            </div>
          ))}
        </div>
        <SubTitle title="效果数据" className="mt-[46px]" />
        <AreaChart
          width={1300}
          height={490}
          data={serviceClassify}
          margin={{
            top: 80,
            right: 30,
            left: 0,
            bottom: 20,
          }}
        >
          {colorList.map((item, index) => (
            <defs key={index}>
              <linearGradient
                id={`colorUv${index}`}
                x1="0"
                y1="0"
                x2="0"
                y2="1"
              >
                <stop offset="5%" stopColor={item[0]} stopOpacity={0.4} />
                <stop offset="95%" stopColor={item[1]} stopOpacity={0} />
              </linearGradient>
            </defs>
          ))}
          <CartesianGrid strokeDasharray="3 3" vertical={false} />
          <XAxis
            dataKey="x"
            stroke="#fff"
            tickLine={false}
            fontSize={24}
            dy={16}
          />
          <YAxis
            stroke="#fff"
            tickLine={false}
            fontSize={24}
            axisLine={false}
            label={{
              value: "单位：Mbps",
              color: "#fff",
              position: "insideTop",
              fontSize: 24,
              fontWeight: 500,
              fontFamily: "PingFang SC",
              dx: 40,
              dy: -60,
              angle: 0,
              fill: "#fff",
            }}
          />
          {/* <Tooltip /> */}
          {serviceClassify.map((_, index) => (
            <Area
              key={index}
              type="linear"
              dataKey={`y${index + 1}`}
              strokeWidth={4}
              stroke={colorList?.[index]?.[0]}
              fill={`url(#colorUv${index})`}
              isAnimationActive={false}
            />
          ))}
        </AreaChart>
      </div>
      {selectItem && (
        <UserList
          key={selectItem.id}
          selectItem={selectItem}
          setSelectItem={setSelectItem}
        />
      )}
    </React.Fragment>
  );
}

export default memo(SubwayInfo);
