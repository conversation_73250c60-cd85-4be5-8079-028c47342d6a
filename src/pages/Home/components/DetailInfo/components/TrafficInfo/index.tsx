import SubTitle from "@/components/SubTitle";
import useStore from "@/store";
import { CloseOutlined } from "@ant-design/icons";
import { Area, AreaChart, CartesianGrid, XAxis, YAxis } from "recharts";
import React, { memo, useMemo, useState } from "react";
import clsx from "clsx";
import UserList from "./components/UserList";

export interface UserInfo {
  level: string;
  dispatchLevel: string;
  guaranteeBandwidth: string;
  communityCount: string;
  connectionCount: number;
  userCount: number;
  id: number;
}

function TrafficInfo() {
  const selectSubwayId = useStore((state) => state.selectSubwayId);

  const [selectItem, setSelectItem] = useState<UserInfo>();

  const onClose = () => {
    useStore.setState((draft) => {
      draft.selectSubwayId = undefined;
    });
  };

  const sceneList: UserInfo[] = [
    {
      level: "游戏级别",
      dispatchLevel: "5QI3",
      guaranteeBandwidth: "200kbps",
      communityCount: "14个",
      connectionCount: 4324,
      userCount: 1001,
      id: 1,
    },
    {
      level: "直播级别",
      dispatchLevel: "5QI4",
      guaranteeBandwidth: "2Mkbps",
      communityCount: "23个",
      connectionCount: 3343,
      userCount: 878,
      id: 1,
    },
    {
      level: "会议级别",
      dispatchLevel: "5QI4",
      guaranteeBandwidth: "4Mkbps",
      communityCount: "3个",
      connectionCount: 843,
      userCount: 558,
      id: 1,
    },
  ];

  const serviceClassify = useMemo(() => {
    const x = [
      "6:55",
      "7:00",
      "7:05",
      "7:10",
      "7:15",
      "7:20",
      "7:25",
      "7:30",
      "7:35",
      "7:40",
      "7:45",
      "7:50",
      "7:55",
      "8:00",
    ];
    const y = [
      2.5, 2.8, 2.1, 4.6, 4.1, 4.4, 4.25, 4.3, 4.1, 4.4, 4.25, 4.3, 4.1, 4.4,
      4.25,
    ];
    return Array.from({ length: 14 }, (_, index) => ({
      x: x[index],
      y: y[index],
    }));
  }, []);

  return (
    <React.Fragment>
      <div className="w-[1408px] h-[1793px] bg-[#2A4160] px-[56px] py-[52px] flex flex-col absolute right-[2772px] top-[280px] pointer-events-auto z-50 shadow-[0px_6px_10px_0px_rgba(0,0,0,0.06)]">
        <div className="absolute right-[50px] top-[50px] z-10">
          <CloseOutlined
            className="text-[42px] text-[#fff] cursor-pointer"
            onClick={onClose}
          />
        </div>
        <SubTitle title="基础信息" />
        <div className="px-[20px] mt-[32px] flex justify-between">
          <div className="w-[392px] h-[192px] bg-gradient-to-r from-[rgba(255,255,255,0.1)] to-[rgba(255,255,255,0)] border-[#5FEAFF] border-l-[6px] p-[32px]">
            <div className="h-[64px] text-[#5CDFEE] text-[48px] flex items-center mb-1">
              区域
            </div>
            <div className="text-[48px] text-[#fff] h-[64px] flex items-center">
              {selectSubwayId}
            </div>
          </div>
          <div className="w-[392px] h-[192px] bg-gradient-to-r from-[rgba(255,255,255,0.1)] to-[rgba(255,255,255,0)] border-[#5FEAFF] border-l-[6px] p-[32px]">
            <div className="h-[64px] text-[#5CDFEE] text-[48px] flex items-center mb-1">
              无线小区数
            </div>
            <div className="text-[48px] text-[#fff] h-[64px] flex items-center">
              13<span className="text-[#84E6FB] text-[26px] ml-[16px]">个</span>
            </div>
          </div>
          <div className="w-[392px] h-[192px] bg-gradient-to-r from-[rgba(255,255,255,0.1)] to-[rgba(255,255,255,0)] border-[#5FEAFF] border-l-[6px] p-[32px]">
            <div className="h-[64px] text-[#5CDFEE] text-[48px] flex items-center mb-1">
              用户数
            </div>
            <div className="text-[48px] text-[#fff] h-[64px] flex items-center">
              2435
              <span className="text-[#84E6FB] text-[26px] ml-[16px]">户</span>
            </div>
          </div>
        </div>
        <SubTitle title="保障信息" className="mt-[75px]" />
        <div className="grid mt-[36px] h-[100px] items-center bg-[#354A63] grid-cols-[190px_190px_190px_190px_220px_220px] gap-[16px] text-[34px] px-[36px]">
          <div>保障级别</div>
          <div>调度级别</div>
          <div>保障带宽</div>
          <div>小区数</div>
          <div>连接数(个)</div>
          <div>用户数(户)</div>
        </div>
        <div className="flex flex-col">
          {sceneList.map((item, index) => (
            <div
              key={index}
              className={clsx(
                "grid h-[100px] items-center grid-cols-[repeat(6,190px)] gap-[16px] text-[34px] px-[36px] hover:bg-[rgba(255,255,255,0.1)] border-b cursor-pointer border-[#fff]",
                {
                  "!bg-[rgba(95,234,255,0.25)]": selectItem?.id === item.id,
                }
              )}
              onClick={() => setSelectItem(item)}
            >
              <div>{item.level}</div>
              <div>{item.dispatchLevel}</div>
              <div>{item.guaranteeBandwidth}</div>
              <div>{item.communityCount}</div>
              <div>{item.connectionCount}</div>
              <div>{item.userCount}</div>
            </div>
          ))}
        </div>
        <SubTitle title="效果数据" className="mt-[46px]" />
        <AreaChart
          width={1300}
          height={690}
          data={serviceClassify}
          margin={{
            top: 80,
            right: 30,
            left: 0,
            bottom: 20,
          }}
        >
          {/* linear-gradient( 90deg, #FFEB3B 0%, #FFEB3B 21%, #FFEB3B 24%, #D43030 25%, #FFEB3B 27%, #43CF7C 29%, #43CF7C 33%, #FFEB3B 36%, #4DD076 42%, #43CF7C 53%, #43CF7C 59%, #43CF7C 69%, #43CF7C 73%, rgba(67,207,124,0) 100%) */}
          <defs>
            <linearGradient
              id="customGradient"
              x1="0%"
              y1="0%"
              x2="100%"
              y2="0%"
              spreadMethod="pad"
            >
              <stop offset="0%" stop-color="#FFEB3B" stop-opacity="0.4" />
              <stop offset="21%" stop-color="#FFEB3B" stop-opacity="0.4" />
              <stop offset="24%" stop-color="#FFEB3B" stop-opacity="0.4" />
              <stop offset="25%" stop-color="#D43030" stop-opacity="0.4" />
              <stop offset="27%" stop-color="#FFEB3B" stop-opacity="0.4" />
              <stop offset="29%" stop-color="#43CF7C" stop-opacity="0.4" />
              <stop offset="33%" stop-color="#43CF7C" stop-opacity="0.4" />
              <stop offset="36%" stop-color="#FFEB3B" stop-opacity="0.4" />
              <stop offset="42%" stop-color="#4DD076" stop-opacity="0.4" />
              <stop offset="53%" stop-color="#43CF7C" stop-opacity="0.4" />
              <stop offset="59%" stop-color="#43CF7C" stop-opacity="0.4" />
              <stop offset="69%" stop-color="#43CF7C" stop-opacity="0.4" />
              <stop offset="73%" stop-color="#43CF7C" stop-opacity="0.4" />
              <stop offset="100%" stop-color="#43CF7C" stop-opacity="0" />
            </linearGradient>
          </defs>
          <CartesianGrid strokeDasharray="3 3" vertical={false} />
          <XAxis
            dataKey="x"
            stroke="#fff"
            tickLine={false}
            fontSize={24}
            dy={16}
          />
          <YAxis
            stroke="#fff"
            tickLine={false}
            fontSize={24}
            axisLine={false}
            ticks={[0, 1, 2, 3, 4, 5]}
            label={{
              value: "单位：Mbps",
              color: "#fff",
              position: "insideTop",
              fontSize: 24,
              fontWeight: 500,
              fontFamily: "PingFang SC",
              dx: 40,
              dy: -60,
              angle: 0,
              fill: "#fff",
            }}
          />
          {/* <Tooltip /> */}
          <Area
            type="monotone"
            dataKey="y"
            strokeWidth={4}
            fill="url(#customGradient)"
            fillOpacity={1}
            stroke="transparent"
            isAnimationActive={false}
          />
        </AreaChart>
      </div>
      {selectItem && (
        <UserList
          key={selectItem.id}
          selectItem={selectItem}
          setSelectItem={setSelectItem}
        />
      )}
    </React.Fragment>
  );
}

export default memo(TrafficInfo);
