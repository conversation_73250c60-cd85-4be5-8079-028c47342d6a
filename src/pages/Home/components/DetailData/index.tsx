import Card from "@/components/Card";
import { memo, useMemo } from "react";
import { orderBy, randomInt } from "es-toolkit";
import useStore from "@/store";
import { EncodingEnum, SceneTypeEnum } from "@/enum";
import { Switch, Case, Default, If, Then } from "react-if";
import { areaList } from "@vant/area-data";
import chinaTreeData from "@pansy/china-division";
import SubwayList from "./components/SubwayList";
import ResidentialCommunityList from "./components/ResidentialCommunityList";

function DetailData() {
  const selectCode = useStore((state) => state.selectCode);

  const sceneType = useStore((state) => state.sceneType);

  // 判断selectCode 是省还是市还是区
  const codeType = useMemo(() => {
    if (selectCode === 100000) {
      // 全国
      return EncodingEnum.China;
      // 判断直辖市
    } else if ([110000, 120000, 310000, 500000].includes(selectCode)) {
      return EncodingEnum.MeCity;
    } else if (areaList.province_list[selectCode]) {
      return EncodingEnum.Province;
    } else if (areaList.city_list[selectCode]) {
      return EncodingEnum.City;
    } else {
      return EncodingEnum.District;
    }
  }, [selectCode]);

  const list = useMemo<Page.ListItem[]>(() => {
    if (sceneType === SceneTypeEnum.District) {
      // 行政区
      const parentCode = selectCode.toString().substring(0, 3);
      const cityItem = chinaTreeData.find(
        (val) => val.value?.substring(0, 3) === parentCode
      );
      const item = cityItem?.children?.find(
        (val) => Number(val.value) === selectCode
      );
      const childList =
        codeType === EncodingEnum.MeCity
          ? cityItem?.children?.[0]?.children
          : item?.children;
      const value =
        childList?.map((val) => ({
          name: val.label,
          key: val.value,
          value: randomInt(0, 20),
        })) ?? [];
      return orderBy(value, ["value"], ["desc"]);
    } else if (sceneType === SceneTypeEnum.Subway) {
      return [];
    }
    return [];
  }, [codeType, sceneType, selectCode]);

  // 省市列表
  const regionList = useMemo<Page.ListItem[]>(() => {
    if (codeType === EncodingEnum.China) {
      const value = [
        { name: "北京市", key: "110000", value: 60 },
        { name: "天津市", key: "120000", value: 32 },
        { name: "河北省", key: "130000", value: 71 },
        { name: "山西省", key: "140000", value: 28 },
        { name: "内蒙古自治区", key: "150000", value: 38 },
        { name: "辽宁省", key: "210000", value: 69 },
        { name: "吉林省", key: "220000", value: 58 },
        { name: "黑龙江省", key: "230000", value: 24 },
        { name: "上海市", key: "310000", value: 37 },
        { name: "江苏省", key: "320000", value: 36 },
        { name: "浙江省", key: "330000", value: 48 },
        { name: "安徽省", key: "340000", value: 31 },
        { name: "福建省", key: "350000", value: 43 },
        { name: "江西省", key: "360000", value: 29 },
        { name: "山东省", key: "370000", value: 88 },
        { name: "河南省", key: "410000", value: 76 },
        { name: "湖北省", key: "420000", value: 50 },
        { name: "湖南省", key: "430000", value: 57 },
        { name: "广东省", key: "440000", value: 107 },
        { name: "广西壮族自治区", key: "450000", value: 27 },
        { name: "海南省", key: "460000", value: 22 },
        { name: "重庆市", key: "500000", value: 30 },
        { name: "四川省", key: "510000", value: 35 },
        { name: "贵州省", key: "520000", value: 23 },
        { name: "云南省", key: "530000", value: 26 },
        { name: "西藏自治区", key: "540000", value: 38 },
        { name: "陕西省", key: "610000", value: 32 },
        { name: "甘肃省", key: "620000", value: 40 },
        { name: "青海省", key: "630000", value: 18 },
        { name: "宁夏回族自治区", key: "640000", value: 19 },
        { name: "新疆维吾尔自治区", key: "650000", value: 39 },
        { name: "香港特别行政区", key: "810000", value: 20 },
        { name: "澳门特别行政区", key: "820000", value: 16 },
        { name: "台湾省", key: "710000", value: 33 },
      ];
      // 根据value正向排序
      return orderBy(value, ["value"], ["desc"]);
    } else if (codeType === EncodingEnum.Province) {
      // 省 展示市列表
      const item = chinaTreeData.find(
        (val) => Number(val.value) === selectCode
      );
      const value =
        item?.children?.map((val) => ({
          name: val.label,
          key: val.value,
          value: randomInt(0, 100),
        })) ?? [];
      return orderBy(value, ["value"], ["desc"]);
    }
    return [];
  }, [codeType, selectCode]);

  return (
    <If condition={sceneType && [SceneTypeEnum.Subway, SceneTypeEnum.ResidentialCommunity].includes(sceneType)}>
      <Then>
        <Card
          title="具体数据"
          className="w-[554px] absolute right-[2186px] top-[280px]"
        >
          <Switch>
            <Case condition={sceneType === SceneTypeEnum.Subway}>
              <SubwayList />
            </Case>
            <Case condition={sceneType === SceneTypeEnum.ResidentialCommunity}>
              <ResidentialCommunityList />
            </Case>
            <Default>
              {/* <div className="h-[1674px] py-[32px]">
                <Scrollbars>
                  <div className="space-y-[16px] px-[32px]">
                    {regionList.map((item) => (
                      <div
                        className="h-[92px] flex justify-between items-center pl-[24px] pr-[12px]"
                        key={item.key}
                      >
                        <span
                          title={item.name}
                          className="text-[rgba(255,255,255,0.85)] text-[36px] w-[280px] truncate"
                        >
                          {item.name}
                        </span>
                        <span className="text-[rgba(255,255,255,0.85)] text-[36px]">
                          {item.value}万户
                        </span>
                      </div>
                    ))}
                  </div>
                </Scrollbars>
              </div> */}
            </Default>
          </Switch>
        </Card>
      </Then>
    </If>
  );
}
export default memo(DetailData);
