import { getAdCodePath } from "@/data/provinceData";
import { EncodingEnum } from "@/enum";
import { getRegionTypeByAdCode } from "@/pages/Home1/utils";
import useStore from "@/store";
import { subwayMapAtom, mapPolygonAtom } from "@/store/atom";
import { GeoUtils } from "@/utils/GeoUtils";
import { useDeepCompareEffect } from "ahooks";
import { randomInt } from "es-toolkit";
import { useAtomValue } from "jotai";
import { selectAtom } from "jotai/utils";
import { useMemo, useRef, useEffect } from "react";
import { Scrollbars } from "react-custom-scrollbars";

function SubwayList() {
  const selectCode = useStore((state) => state.selectCode);
  const selectSubwayId = useStore((state) => state.selectSubwayId);

  // 获取当前选中的地图多边形区域
  const mapPolygon = useAtomValue(mapPolygonAtom);

  const subwayProvinceCode = useMemo(() => {
    const code = getAdCodePath(selectCode.toString())?.[0]?.value;
    const type = getRegionTypeByAdCode(code);
    if (type === EncodingEnum.MeCity) {
      return code;
    }
    return selectCode;
  }, [selectCode]);

  // 检查地铁线路是否在选中的多边形区域内
  const isPointListInPolygon = (
    stationList: BMapGL.Point[],
    polygon: BMapGL.Polygon | null
  ) => {
    if (!polygon) return true; // 如果没有选中区域，显示所有线路
    // 检查列表的任意一个点是否在多边形内
    return stationList?.some((point) => {
      return GeoUtils.isPointInPolygon(point, polygon);
    });
  };

  // 使用优化的线段分割方法，考虑与多边形边界的实际交点
  const splitLineByRegion = (
    line: BMapGL.Point[],
    polygon: BMapGL.Polygon | null
  ): { points: BMapGL.Point[]; isInRegion: boolean }[] => {
    return GeoUtils.splitLineByPolygonIntersection(line, polygon);
  };

  const numberList = useMemo(() => {
    // 返回一个数组长度是20的随机数字列表
    return Array.from({ length: 20 }, () => randomInt(0, 20));
  }, []);

  const mapRefState = useStore((state) => state.mapRefState);

  // 存储地图上的地铁路线覆盖物
  const subwayOverlays = useRef<
    (BMapGL.Polyline | BMapGL.Marker | BMapGL.Label)[]
  >([]);

  const list = useAtomValue(
    useMemo(
      () =>
        selectAtom(subwayMapAtom, (state) => {
          const code = subwayProvinceCode.toString()?.slice(0, 4);
          const subwayItems = state.get(code) || [];
          if (!mapPolygon) {
            return [];
          }
          // 根据 mapPolygon 过滤地铁线路
          const filteredItems = subwayItems.filter((item) =>
            isPointListInPolygon(
              item.stationList?.map((val) => val.position),
              mapPolygon
            )
          );

          return filteredItems.map((item, index) => ({
            key: item.name, // 使用 name 作为 key
            value: numberList[index],
            ...item,
          }));
        }),
      [subwayProvinceCode, mapPolygon, numberList]
    )
  );

  useDeepCompareEffect(() => {
    // 清除之前的覆盖物
    subwayOverlays.current.forEach((overlay) => {
      mapRefState?.removeOverlay(overlay);
    });
    subwayOverlays.current = [];
    // 已经添加的站点和名称 不需要重复添加
    const stationNameList: string[] = [];

    list.forEach((item) => {
      const lineColor = item.color;

      // 将线路拆分成多个段
      const lineSegments = splitLineByRegion(item.line, mapPolygon!);

      // 默认为1的透明度和0.3的透明度变量
      const defaultOpacity = 1;
      const transparentOpacity = 0.3;

      // 为每个段创建不同透明度的线路
      lineSegments.forEach((segment) => {
        if (segment.points.length >= 2) {
          const strokeOpacity = segment.isInRegion
            ? defaultOpacity
            : transparentOpacity;
          const subwayLine = new BMapGL.Polyline(segment.points, {
            strokeColor: lineColor,
            strokeWeight: 2,
            strokeOpacity: strokeOpacity,
          });
          mapRefState?.addOverlay(subwayLine);
          subwayOverlays.current.push(subwayLine);
        }
      });

      // 为每个站点创建标记和标签，根据是否在区域内设置透明度
      for (const station of item.stationList ?? []) {
        if (stationNameList.includes(station.name)) {
          // 不重复添加站点
          continue;
        }

        const isStationInRegion = mapPolygon
          ? GeoUtils.isPointInPolygon(station.position, mapPolygon)
          : true;
        const opacity = isStationInRegion ? defaultOpacity : transparentOpacity;

        // 创建站点标记
        const circleIcon = new BMapGL.Icon(
          "data:image/svg+xml;base64," +
            btoa(`
          <svg width="8" height="8" xmlns="http://www.w3.org/2000/svg">
            <circle cx="4" cy="4" r="3" fill="white" stroke="${lineColor}" 
                    stroke-width="1" opacity="${opacity}"/>
          </svg>
        `),
          new BMapGL.Size(8, 8),
          {
            anchor: new BMapGL.Size(5, 5),
          }
        );
        const marker = new BMapGL.Marker(station.position, {
          icon: circleIcon,
        });
        mapRefState?.addOverlay(marker);
        subwayOverlays.current.push(marker);

        // 创建站点标签
        const label = new BMapGL.Label(station.name, {
          position: station.position,
          offset: new BMapGL.Size(-10, 10),
        });
        label.setStyle({
          fontSize: "0.38vw",
          color: `rgba(255,255,255,${0.85 * opacity})`,
          background: "none",
          fontWeight: "normal",
          border: "none",
        });
        mapRefState?.addOverlay(label);
        subwayOverlays.current.push(label);
        stationNameList.push(station.name);
      }
    });

    // 清理函数：组件卸载时清除所有覆盖物
    return () => {
      subwayOverlays.current.forEach((overlay) => {
        mapRefState?.removeOverlay(overlay);
      });
      subwayOverlays.current = [];
    };
  }, [list, mapPolygon]);

  // 组件卸载时清理覆盖物
  useEffect(() => {
    return () => {
      subwayOverlays.current.forEach((overlay) => {
        mapRefState?.removeOverlay(overlay);
      });
      subwayOverlays.current = [];
    };
  }, [mapRefState]);

  return (
    <div className="h-[1674px] py-[32px]">
      <Scrollbars>
        <div className="space-y-[16px]">
          {list.map((item) => (
            <div
              className={`h-[92px] pl-[40px] pr-[40px] flex justify-between cursor-pointer items-center ${
                selectSubwayId === item.key
                  ? "bg-[#384E67]"
                  : "hover:bg-[rgba(255,255,255,0.05)]"
              }`}
              key={item.key}
              onClick={() => {
                useStore.setState((draft) => {
                  draft.selectSubwayId = item.key;
                });
              }}
            >
              <span
                title={item.name}
                className="text-[rgba(255,255,255,0.85)] text-[36px] w-[280px] truncate"
              >
                <span
                  className="size-[24px] rounded-full border-2 border-white inline-block mr-5"
                  style={{
                    background: item.color,
                  }}
                ></span>
                {item.name}
              </span>
              <span className="text-[rgba(255,255,255,0.85)] text-[36px]">
                {item.value}万户
              </span>
            </div>
          ))}
        </div>
      </Scrollbars>
    </div>
  );
}
export default SubwayList;
