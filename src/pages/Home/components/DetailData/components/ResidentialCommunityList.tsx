import React, { useMemo } from "react";
import Scrollbars from "react-custom-scrollbars";
import useStore from "@/store";
import { useMount } from "ahooks";
import { useImmerAtom } from "jotai-immer";
import { communityMapAtom } from "@/store/atom";
import { convertBaiduGeoToPointsWithMap } from "@/utils";

function ResidentialCommunityList() {
  const selectCommunityId = useStore((state) => state.selectCommunityId);
  const mapRefState = useStore((state) => state.mapRefState);
  const [communityMap, setCommunityMap] = useImmerAtom(communityMapAtom);

  const list = useMemo(() => {
    return communityMap.get("1200") || [];
  }, [communityMap]);

  useMount(() => {
    const arr = [
      {
        address: "山东省济南市槐荫区济齐路112号",
        area: "槐荫区",
        city: "济南市",
        detail: 1,
        location: { lat: 36.680394, lng: 116.958957 },
        name: "匡山小区",
        province: "山东省",
        street_id: "15e1e85e8df19bdc581093c5",
        uid: "15e1e85e8df19bdc581093c5",
      },
    ];
    setCommunityMap((draft) => {
      draft.set("1200", arr);
    });
  });

  // 获取小区边界数据
  const getCommunityBoundaryData = async (uid: string) => {
    return new Promise((resolve) => {
      const callbackName = 'baiduCallback' + Date.now();
      
      const script = document.createElement('script');
      script.src = `https://map.baidu.com/?pcevaname=pc4.1&qt=ext&ext_ver=new&l=12&uid=${uid}&callback=${callbackName}`;
      
      (window as any)[callbackName] = function(data: any) {
        delete (window as any)[callbackName];
        document.head.removeChild(script);
        
        if (data && data.content && data.content.geo) {
          resolve({
            uid,
            geo: data.content.geo,
            name: data.content.name || '',
            address: data.content.addr || ''
          });
        } else {
          resolve(null);
        }
      };
      
      document.head.appendChild(script);
      
      setTimeout(() => {
        if ((window as any)[callbackName]) {
          delete (window as any)[callbackName];
          if (script.parentNode) {
            document.head.removeChild(script);
          }
          resolve(null);
        }
      }, 5000);
    });
  };

  const handleSelectCommunity = async (uid: string) => {
    useStore.setState((draft) => {
      draft.selectCommunityId = uid;
    });

    if (!mapRefState) {
      console.warn('地图实例未初始化');
      return;
    }

    const selectedCommunity = list.find(item => item.uid === uid);

    mapRefState.centerAndZoom(new BMapGL.Point(selectedCommunity?.location.lng || 0, selectedCommunity?.location.lat || 0), 15);

    try {
      // 清除之前的覆盖物
      const overlays = mapRefState.getOverlays();
      overlays.forEach(overlay => {
        if (overlay instanceof BMapGL.Polygon) {
          mapRefState.removeOverlay(overlay);
        }
      });

      // 获取边界数据
      const boundaryData = await getCommunityBoundaryData(uid) as any;
      
      const boundaryPoints = convertBaiduGeoToPointsWithMap(boundaryData.geo);

      console.log(boundaryPoints,'222');
      
      
    } catch (error) {
      console.error('处理小区边界失败:', error);
    }
  };
  return (
    <React.Fragment>
      <div className="h-[1674px] py-[32px]">
        <Scrollbars>
          <div className="space-y-[16px]">
            {list.map((item,index) => (
              <div
                key={index}
                className={`h-[92px] pl-[40px] pr-[40px] flex justify-between cursor-pointer items-center ${
                  selectCommunityId === item.uid
                    ? "bg-[#384E67]"
                    : "hover:bg-[rgba(255,255,255,0.05)]"
                }`}
                onClick={() => {
                  handleSelectCommunity(item.uid);
                }}
              >
                <span
                  title={item.name}
                  className="text-[rgba(255,255,255,0.85)] text-[36px] w-[280px] truncate"
                >
                  {item.name}
                </span>
                {index}
              </div>
            ))}
          </div>
        </Scrollbars>
      </div>
    </React.Fragment>
  );
}

export default ResidentialCommunityList;
