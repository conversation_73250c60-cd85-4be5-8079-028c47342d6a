import Header from "./components/Header";
import Common from "./components/Common";
import MenuList from "./components/MenuList";
import ProvinceList from "./components/ProvinceList";
import DetailData from "./components/DetailData";
import SceneList from "./components/SceneList";
import Center from "./components/Center";
import UpdateDataComponent from "./components/UpdateDataComponent";
import React from "react";
import { Spin } from "antd";
import useStore from "@/store";
import ReactScaleScreen from "@/components/ReactScaleScreen";
import { PAGE_HEIGHT, PAGE_WIDTH } from "@/utils/config";
import DetailInfo from "./components/DetailInfo";

function HomePage() {
  const loading = useStore((state) => state.loading);

  return (
    <React.Fragment>
      <div className="w-screen h-screen flex flex-col">
        <ReactScaleScreen
          className="bg-[#081525]"
          width={PAGE_WIDTH}
          height={PAGE_HEIGHT}
          style={{
            willChange: "transform",
            backfaceVisibility: "hidden",
            perspective: 1000,
          }}
          ignoreChildren={<Center />}
        >
          <div
            className="w-full h-full relative overflow-hidden"
            style={{
              opacity: loading ? 0 : 1,
            }}
          >
            <Header />
            <Common />
            <MenuList />
            <ProvinceList />
            <SceneList />
            <DetailData />
            <DetailInfo />
            <UpdateDataComponent />
          </div>
          <Spin spinning={loading} className="centered" />
        </ReactScaleScreen>
      </div>
    </React.Fragment>
  );
}

export default HomePage;
