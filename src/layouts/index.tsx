import type { ThemeConfig } from "antd";
import { ConfigProvider, theme } from "antd";
import zhCN from "antd/locale/zh_CN";
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
import { useMemo } from "react";
import { Outlet } from "umi";
dayjs.locale("zh-cn");

export default function Layout() {
  const themeConfig = useMemo(() => {
    const value: ThemeConfig = {
      token: {
        colorPrimary: "#5CDFEE",
      },
      algorithm: theme.darkAlgorithm,
      components: {
        Input: {
          colorBgContainer: "rgba(255,255,255,0.08)",
          colorBorder: "transparent",
          colorPrimaryHover: "transparent",
          colorPrimary: "rgba(255,255,255,0)",
          borderRadius: 8,
          controlHeight: 80,
          fontSize: 30,
        },
        InputNumber: {
          colorBgContainer: "rgba(255,255,255,0.08)",
          colorBorder: "transparent",
          colorPrimaryHover: "transparent",
          colorPrimary: "rgba(255,255,255,0)",
          borderRadius: 8,
          controlHeight: 80,
          paddingInline:30,
          fontSize: 30,
        },
        Tree: {
          colorBgContainer: "transparent",
          titleHeight: 100,
          // colorBgSpotlight: "transparent",
        },
        Spin: {
          dotSize: 100,
        },
        Select: {
          controlHeight: 80,
          fontSize: 32,
          paddingSM: 40,
          borderRadius: 6,
          optionPadding: "20px 32px",
          colorBgContainer: "#374C6A",
          colorBorder: "#5FEAFF",
          lineWidth: 0,
          colorBgElevated: "#304765",
          optionSelectedBg: "rgba(95,234,255,.75)",
        },
        Segmented: {
          controlHeight: 128,
          fontSize: 42,
          itemSelectedBg: "#19C2DB",
          controlPaddingHorizontal: 206,
          trackBg: "rgba(255,255,255,0.08)",
          trackPadding: 0,
          itemSelectedColor: "#FFFFFF",
        },
        Tabs: {
          colorText: "#94AABD",
          fontSize: 24,
          horizontalItemGutter: 40,
          horizontalItemPadding: "20px 0",
          lineWidth: 0,
          lineWidthBold: 2,
        },
        Table: {
          headerBg: "#3F5470",
          fontSize: 42,
          colorBgContainer: "#2A4160",
          cellPaddingInline: 60,
          cellPaddingBlock: 16,
          borderColor: "rgba(255, 255, 255, 0.30)",
        },
        Pagination: {
          itemActiveBg: "rgba(255,255,255,0.15)",
          colorPrimary: "#5FEAFF",
          itemSize: 56,
          itemBg: "rgba(0,0,0,0)",
          itemActiveColorDisabled: "red",
        },
        Modal: {
          contentBg: "#233954",
          headerBg: "#233954",
          colorBgMask: "rgba(0,0,0,0.55)",
          titleFontSize: 42,
          borderRadius: 32,
        },
        Form: {
          fontSize: 30,
          labelColor: "rgba(255,255,255,0.65)",
          controlHeight: 80,
        },
        Radio: {
          dotSize: 14,
          radioSize: 32,
          fontSize: 30,
          buttonCheckedBg:"red",
          colorBgContainer:"transparent",
          colorBorder: "rgba(255,255,255,0.6)",
        },
      },
    };
    return value;
  }, []);

  return (
    <ConfigProvider
      locale={zhCN}
      wave={{
        disabled: true, // 禁用 wave
      }}
      theme={themeConfig}
    >
      <Outlet />
    </ConfigProvider>
  );
}
