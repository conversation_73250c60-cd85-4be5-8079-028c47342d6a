@font-face {
  font-family: "iconfont"; /* Project id 5011818 */
  src: url('iconfont.woff2?t=1756812178369') format('woff2'),
       url('iconfont.woff?t=1756812178369') format('woff'),
       url('iconfont.ttf?t=1756812178369') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-icon10:before {
  content: "\e70b";
}

.icon-icon6:before {
  content: "\e70c";
}

.icon-icon2:before {
  content: "\e70d";
}

.icon-icon3:before {
  content: "\e70e";
}

.icon-icon5:before {
  content: "\e70f";
}

.icon-icon4:before {
  content: "\e710";
}

.icon-icon12:before {
  content: "\e711";
}

.icon-icon11:before {
  content: "\e712";
}

.icon-icon9:before {
  content: "\e713";
}

.icon-icon7:before {
  content: "\e714";
}

.icon-icon13:before {
  content: "\e708";
}

.icon-icon8:before {
  content: "\e709";
}

.icon-icon1:before {
  content: "\e70a";
}

