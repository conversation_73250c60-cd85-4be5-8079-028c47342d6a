import { atomWithImmer } from "jotai-immer";
import { enableMapSet } from "immer";
import { atom, createStore } from "jotai";

enableMapSet();

/**
 * 地铁线路数据
 */
export const subwayMapAtom = atomWithImmer(new Map<string, Page.SubwayMapItem[]>());

/**
 * 住宅小区数据
 */
export const communityMapAtom = atomWithImmer(new Map<string, Page.CommunityMapItem[]>());

/**
 * 当前选中小区的边界数据
 */
export const currentCommunityBoundaryAtom = atom<Page.CommunityBoundaryData | null>(null);

/**
 * 当前地图上的小区边界多边形
 */
export const communityPolygonAtom = atom<BMapGL.Polygon | null>(null);

/**
 * 当前选中的区域Polygon
 */
export const mapPolygonAtom = atom<BMapGL.Polygon>();

const AtomStore = createStore();

export default AtomStore;