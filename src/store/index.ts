import { immer } from "zustand/middleware/immer";
import { shallow } from "zustand/shallow";
import { createWithEqualityFn } from "zustand/traditional";
import { AbilityEnum, type SceneTypeEnum } from "@/enum";
import { enableMapSet } from "immer";
import { CHINA_CODE } from "@/constant";

enableMapSet()

interface GlobalState {
  token: string;
  selectCode: number;
  mapRefState?: BMapGL.Map;
  /**
   * 选中的能力id
   */
  selectAbilityType: AbilityEnum;
  /**
   * 页面初始化加载需要地图加载完成再渲染出来
   */
  loading: boolean;
  /**
   * 场景类型
   */
  sceneType?: SceneTypeEnum;
  /**
   * 地铁线路列表
   */
  subwayLineList: Page.SubwayLineItem[]
  /**
   * 选中的地铁线路名称
   */
  selectSubwayId?: string;
  /**
   * 选中的住宅小区名称
   */
  selectCommunityId?: string;
}

const useStore = createWithEqualityFn<GlobalState>()(
  immer((set, get) => ({
    token: "",
    selectCode: CHINA_CODE,
    mapRefState: undefined,
    selectAbilityType: AbilityEnum.UserPriorityScheduling,
    loading: true,
    subwayRefState: undefined,
    sceneType: undefined,
    subwayLineList: [],
    selectSubwayId: undefined,
    selectCommunityId: undefined,
  })),
  shallow
);

export default useStore;
