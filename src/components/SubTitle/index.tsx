import bgIcon from "@/assets/images/headerBg.png";
import { memo } from "react";

interface IProps {
  title: string;
  className?: string;
}
function SubTitle(props: IProps) {
  const { title, className = "" } = props;

  return (
    <div
      className={`h-[80px] flex items-start relative bg-cover bg-center bg-no-repeat w-full ${className}`}
      style={{
        backgroundImage: `url(${bgIcon})`,
      }}
    >
      <div
        className="text-[42px] ml-[130px]"
        style={{
          WebkitBackgroundClip: "text",
          WebkitTextFillColor: "transparent",
          backgroundImage:
            "linear-gradient(180deg, #DFF0FA 19%, #E0F0FF 46%, #A8D4F9 82%)",
        }}
      >
        {title}
      </div>
    </div>
  );
}
export default memo(SubTitle);
