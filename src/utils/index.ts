// src/pages/home/<USER>
export interface PolygonData {
  id: string;
  name: string;
  path: BMapGL.Point[];
  properties: any;
}

export interface FanShapedParams {
  startRatio: number;
  endRatio: number;
  isSelected: boolean;
  isHovered: boolean;
  k?: number;
  h: number;
}


/**
 * 将坐标数组转换为 BMapGL.Point 数组
 * @param coordinates [lng, lat][] 坐标数组
 * @returns BMapGL.Point[]
 */
function convertCoordinatesToPoints(coordinates: number[][]): BMapGL.Point[] {
  return coordinates.map(([lng, lat]) => new BMapGL.Point(lng, lat));
}

/**
 * 根据区域属性获取多边形样式
 * @param properties 区域属性
 * @returns 样式对象
 */
export function getPolygonStyle(properties: any) {
  const colors = ['#384D68', '#4A5D7A', '#5C6E8C', '#6E7F9E', '#8090B0'];
  const colorIndex = (properties.join || 0) % colors.length;

  return {
    strokeColor: "#FFFFFF",
    strokeWeight: 1,
    fillColor: colors[colorIndex],
    fillOpacity: 0.6
  };
}

/**
 * 计算多边形的中心点（质心）
 * @param points BMapGL.Point[] 多边形的点数组
 * @returns BMapGL.Point 中心点
 */
export function calculatePolygonCenter(points: BMapGL.Point[]): BMapGL.Point {
  if (points.length === 0) {
    return new BMapGL.Point(0, 0);
  }

  let totalLng = 0;
  let totalLat = 0;

  points.forEach(point => {
    totalLng += point.lng;
    totalLat += point.lat;
  });

  return new BMapGL.Point(
    totalLng / points.length,
    totalLat / points.length
  );
}

/**
 * 将 GeoJSON FeatureCollection 转换为 Polygon 组件所需的数据格式
 * @param featureCollection GeoJSON FeatureCollection
 * @param isOutline 是不是轮廓coordinates取第一个就可以
 * @returns PolygonData[]
 */
export function convertGeoJSONToPolygonData(featureCollection: any, isOutline: boolean = false): PolygonData[] {
  const polygonDataList: PolygonData[] = [];

  // 遍历所有 features
  featureCollection?.features.forEach((feature: any, index: number) => {
    const { geometry, properties } = feature;

    // 只处理 Polygon 和 MultiPolygon 类型，跳过 Point 类型
    if (geometry.type === 'Polygon') {
      // Polygon: coordinates[0] 是外轮廓，coordinates[1+] 是内部孔洞
      const outerRing = geometry.coordinates[0];
      const path = convertCoordinatesToPoints(outerRing);

      polygonDataList.push({
        id: `${properties.adcode}-${index}`,
        name: properties.name,
        path,
        properties
      });

    } else if (geometry.type === 'MultiPolygon') {
      // MultiPolygon: 每个 polygon 都是一个独立的形状
      const coordinates = isOutline ? [geometry.coordinates?.[0] ? geometry.coordinates?.[0] : []] : geometry.coordinates;
      coordinates.forEach((polygonCoords: number[][][], polyIndex: number) => {
        // 取每个 polygon 的外轮廓 (polygonCoords[0])
        const outerRing = polygonCoords[0];
        const path = convertCoordinatesToPoints(outerRing);

        polygonDataList.push({
          id: `${properties.adcode}-${index}-${polyIndex}`,
          name: properties.name,
          path,
          properties
        });
      });
    }
    // 跳过 Point 类型，因为 Polygon 组件不需要
  });

  return polygonDataList;
}


// 每隔3位,分割方法
export const onFormatSplit = (num: number) => {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
};



export function getFanShapedSurface(params: FanShapedParams) {
  const { startRatio, endRatio, isSelected, isHovered, k = 0, h } = params;

  // 如果只有一个扇形，则不实现选中效果。
  const selectCondition = isSelected && startRatio !== 0 && endRatio !== 1;
  const midRatio = (startRatio + endRatio) / 2;
  const startRadian = startRatio * Math.PI * 2;
  const endRadian = endRatio * Math.PI * 2;
  const midRadian = midRatio * Math.PI * 2;

  // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
  const offsetX = selectCondition ? Math.cos(midRadian) * 0.1 : 0;
  const offsetY = selectCondition ? Math.sin(midRadian) * 0.1 : 0;

  // 计算高亮效果的放大比例（未高亮，则比例为 1）
  const hoverRate = isHovered ? 1.05 : 1;

  // 返回曲面参数方程
  return {
    u: {
      min: -Math.PI,
      max: Math.PI * 3,
      step: Math.PI / 32,
    },

    v: {
      min: 0,
      max: Math.PI * 2,
      step: Math.PI / 20,
    },

    x(u: number, v: number) {
      if (u < startRadian) {
        return (
          offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate
        );
      }
      if (u > endRadian) {
        return (
          offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate
        );
      }
      return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate;
    },

    y(u: number, v: number) {
      if (u < startRadian) {
        return (
          offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate
        );
      }
      if (u > endRadian) {
        return (
          offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate
        );
      }
      return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate;
    },

    z(u: number, v: number) {
      if (u < -Math.PI * 0.5) {
        return Math.sin(u);
      }
      if (u > Math.PI * 2.5) {
        return Math.sin(u) * h * 0.1;
      }
      // 当前图形的高度是Z根据h（每个value的值决定的）
      return Math.sin(v) > 0 ? 1 * h * 0.1 : -1;
    },
  };
}

// 求百分比 保留两位
export function getPercent(value: number, total: number) {
  return ((value / total) * 100).toFixed(2);
}

// 数字改成万返回 例如 32000返回 3.2
export function getNumberWithWan(value: number) {
  return (value / 10000).toFixed(2);
}

/**
 * 将百度米制坐标转换为经纬度坐标
 * @param geo 百度米制坐标字符串
 * @returns BMapGL.Point[] 经纬度坐标数组
 */
export function convertBaiduGeoToPoints(geo: string): BMapGL.Point[] {
  if (!geo) return [];
  
  try {
    // 解析坐标字符串，通常格式为 "1|x1,y1;x2,y2;..."
    const parts = geo.split('|');
    if (parts.length < 2) return [];
    
    const coordString = parts[1];
    const coordPairs = coordString.split(';');
    
    const points: BMapGL.Point[] = [];
    
    coordPairs.forEach(pair => {
      const [x, y] = pair.split(',').map(Number);
      if (!isNaN(x) && !isNaN(y)) {
        // 这里需要使用BMAP_NORMAL_MAP.getProjection()来转换坐标
        // 但由于我们在工具函数中，需要传入map实例或使用其他方式
        // 暂时直接除以100来近似转换（这是一个简化的处理）
        const lng = x / 100;
        const lat = y / 100;
        points.push(new BMapGL.Point(lng, lat));
      }
    });
    
    return points;
  } catch (error) {
    console.error('坐标转换失败:', error);
    return [];
  }
}

/**
 * 将百度米制坐标转换为经纬度
 * @param x 米制坐标x
 * @param y 米制坐标y
 * @returns BMapGL.Point 经纬度坐标
 */
function convertMercatorToLatLng(x: number, y: number): BMapGL.Point | null {
  try {
    // 百度地图使用的是BD09墨卡托投影
    // 这里使用百度地图的转换常数进行逆转换
    const MCBAND = [12890594.86, 8362377.87, 5591021, 3481989.83, 1678043.12, 0];
    const MC2LL = [
      [1.410526172116255e-8, 0.00000898305509648872, -1.9939833816331, 200.9824383106796, -187.2403703815547, 91.6087516669843, -23.38765649603339, 2.57121317296198, -0.03801003308653, 17337981.2],
      [-7.435856389565537e-9, 0.000008983055097726239, -0.78625201886289, 96.32687599759846, -1.85204757529826, -59.36935905485877, 47.40033549296737, -16.50741931063887, 2.28786674699375, 10260144.86],
      [-3.030883460898826e-8, 0.00000898305509983578, 0.30071316287616, 59.74293618442277, 7.357984074871, -25.38371002664745, 13.45380521110908, -3.29883767235584, 0.32710905363475, 6856817.37],
      [-1.981981304930552e-8, 0.000008983055099779535, 0.03278182852591, 40.31678527705744, 0.65659298677277, -4.44255534477492, 0.85341911805263, 0.12923347998204, -0.04625736007561, 4482777.06],
      [3.09191371068437e-9, 0.000008983055096812155, 0.00006995724062, 23.10934304144901, -0.00023663490511, -0.6321817810242, -0.00663494467273, 0.03430082397953, -0.00466043876332, 2555164.4],
      [2.890871144776878e-9, 0.000008983055095805407, -3.068298e-8, 7.47137025468032, -0.00000353937994, -0.02145144861037, -0.00001234426596, 0.00010322952773, -0.00000323890364, 826088.5]
    ];

    let temp = Math.abs(y);
    let cF = null;
    for (let cE = 0; cE < MCBAND.length; cE++) {
      if (temp >= MCBAND[cE]) {
        cF = MC2LL[cE];
        break;
      }
    }
    if (!cF) {
      for (let cE = MCBAND.length - 1; cE >= 0; cE--) {
        if (temp >= MCBAND[cE]) {
          cF = MC2LL[cE];
          break;
        }
      }
    }
    
    if (cF) {
      const T = cF[9];
      const cB = Math.abs(x) / T;
      const cC = Math.abs(y) / T;
      let cD = cF[0] + cF[1] * cB + cF[2] * cB * cB + cF[3] * cB * cB * cB + cF[4] * cB * cB * cB * cB + cF[5] * cB * cB * cB * cB * cB + cF[6] * cB * cB * cB * cB * cB * cB + cF[7] * cB * cB * cB * cB * cB * cB * cB + cF[8] * cB * cB * cB * cB * cB * cB * cB * cB;
      cD *= (x < 0 ? -1 : 1);
      let cE = Math.abs(cC) / cF[9];
      cE = cF[0] + cF[1] * cE + cF[2] * cE * cE + cF[3] * cE * cE * cE + cF[4] * cE * cE * cE * cE + cF[5] * cE * cE * cE * cE * cE + cF[6] * cE * cE * cE * cE * cE * cE + cF[7] * cE * cE * cE * cE * cE * cE * cE + cF[8] * cE * cE * cE * cE * cE * cE * cE * cE;
      cE *= (y < 0 ? -1 : 1);
      
      return new BMapGL.Point(cD, cE);
    }
    
    return null;
  } catch (error) {
    console.error('墨卡托坐标转换失败:', error, { x, y });
    return null;
  }
}

/**
 * 将百度米制坐标转换为经纬度坐标
 * @param geo 百度米制坐标字符串
 * @returns BMapGL.Point[] 经纬度坐标数组
 */
export function convertBaiduGeoToPointsWithMap(geo: string): BMapGL.Point[] {
  if (!geo) return [];
  
  try {
    // 解析坐标字符串，格式例如："4|13019485.904418,4368445.639555;13020371.415605,4369554.662478|1-13020120.4629632,4369359.9860995,..."
    const parts = geo.split('|');
    if (parts.length < 2) return [];
    
    const points: BMapGL.Point[] = [];
    
    // 处理第二部分的坐标（用分号分隔的坐标对）
    if (parts[1]) {
      const coordPairs = parts[1].split(';');
      coordPairs.forEach(pair => {
        if (pair.trim()) {
          const [x, y] = pair.split(',').map(Number);
          if (!isNaN(x) && !isNaN(y)) {
            const point = convertMercatorToLatLng(x, y);
            if (point) points.push(point);
          }
        }
      });
    }
    
    // 处理第三部分的坐标（如果存在，格式为 "1-x1,y1,x2,y2,..."）
    if (parts[2]) {
      const coordString = parts[2].startsWith('1-') ? parts[2].substring(2) : parts[2];
      const coords = coordString.split(',').map(Number);
      
      // 每两个数字为一对坐标
      for (let i = 0; i < coords.length; i += 2) {
        if (i + 1 < coords.length) {
          const x = coords[i];
          const y = coords[i + 1];
          if (!isNaN(x) && !isNaN(y)) {
            const point = convertMercatorToLatLng(x, y);
            if (point) points.push(point);
          }
        }
      }
    }
    
    return points;
  } catch (error) {
    console.error('百度米制坐标转换失败:', error);
    return [];
  }
}