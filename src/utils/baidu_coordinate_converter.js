/**
 * 百度坐标转换JavaScript库
 * 支持BD-09MC（百度米制）转换为BD-09（百度经纬度）和WGS84坐标系统
 */

class BaiduCoordinateConverter {
    constructor() {
        // BD-09MC转BD-09的转换参数
        this.MCBAND = [12890594.86, 8362377.87, 5591021, 3481989.83, 1678043.12, 0];
        this.MC2LL = [
            [1.410526172116255e-8, 0.00000898305509648872, -1.9939833816331, 200.9824383106796, -187.2403703815547, 91.6087516669843, -23.38765649603339, 2.57121317296198, -0.03801003308653, 17337981.2],
            [-7.435856389565537e-9, 0.000008983055097726239, -0.78625201886289, 96.32687599759846, -1.85204757529826, -59.36935905485877, 47.40033549296737, -16.50741931063887, 2.28786674699375, 10260144.86],
            [-3.030883460898826e-8, 0.00000898305509983578, 0.30071316287616, 59.74293618442277, 7.357984074871, -25.38371002664745, 13.45380521110908, -3.29883767235584, 0.32710905363475, 6856817.37],
            [-1.981981304930552e-8, 0.000008983055099779535, 0.03278182852591, 40.31678527705744, 0.65659298677277, -4.44255534477492, 0.85341911805263, 0.12923347998204, -0.04625736007561, 4482777.06],
            [3.09191371068437e-9, 0.000008983055096812155, 0.00006995724062, 23.10934304144901, -0.00023663490511, -0.6321817810242, -0.00663494467273, 0.03430082397953, -0.00466043876332, 2555164.4],
            [2.890871144776878e-9, 0.000008983055095805407, -3.068298e-8, 7.47137025468032, -0.00000353937994, -0.02145144861037, -0.00001234426596, 0.00010322952773, -0.00000323890364, 826088.5]
        ];
        
        // BD-09转WGS84的转换参数
        this.X_PI = 3.14159265358979324 * 3000.0 / 180.0;
        this.PI = 3.1415926535897932384626;
        this.A = 6378245.0;
        this.EE = 0.00669342162296594323;
    }

    /**
     * 百度米制坐标转百度经纬度坐标
     * @param {number} x - 百度米制X坐标
     * @param {number} y - 百度米制Y坐标
     * @returns {Object} {lng: 经度, lat: 纬度}
     */
    mc2ll(x, y) {
        const absX = Math.abs(x);
        const absY = Math.abs(y);
        
        // 确定使用哪个转换参数
        let mc = this.MC2LL[this.MC2LL.length - 1];
        for (let i = 0; i < this.MCBAND.length; i++) {
            if (absY >= this.MCBAND[i]) {
                mc = this.MC2LL[i];
                break;
            }
        }
        
        // 执行转换计算
        let lng = mc[0] + mc[1] * absX;
        const c = absY / mc[9];
        let lat = mc[2] + mc[3] * c + mc[4] * c * c + mc[5] * c * c * c +
                  mc[6] * c * c * c * c + mc[7] * c * c * c * c * c +
                  mc[8] * c * c * c * c * c * c;
        
        // 处理负坐标
        lng *= x < 0 ? -1 : 1;
        lat *= y < 0 ? -1 : 1;
        
        return { lng, lat };
    }

    /**
     * BD-09坐标转WGS84坐标
     * @param {number} lng - BD-09经度
     * @param {number} lat - BD-09纬度
     * @returns {Object} {lng: WGS84经度, lat: WGS84纬度}
     */
    bd09ToWgs84(lng, lat) {
        // BD-09转GCJ-02
        const x = lng - 0.0065;
        const y = lat - 0.006;
        const z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * this.X_PI);
        const theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * this.X_PI);
        const gcjLng = z * Math.cos(theta);
        const gcjLat = z * Math.sin(theta);
        
        // GCJ-02转WGS84
        const dlat = this._transformLat(gcjLng - 105.0, gcjLat - 35.0);
        const dlng = this._transformLng(gcjLng - 105.0, gcjLat - 35.0);
        
        const radlat = gcjLat / 180.0 * this.PI;
        let magic = Math.sin(radlat);
        magic = 1 - this.EE * magic * magic;
        const sqrtmagic = Math.sqrt(magic);
        const dlatFinal = (dlat * 180.0) / ((this.A * (1 - this.EE)) / (magic * sqrtmagic) * this.PI);
        const dlngFinal = (dlng * 180.0) / (this.A / sqrtmagic * Math.cos(radlat) * this.PI);
        
        const mglat = gcjLat - dlatFinal;
        const mglng = gcjLng - dlngFinal;
        
        return { lng: mglng, lat: mglat };
    }

    /**
     * 纬度转换辅助函数
     * @private
     */
    _transformLat(lng, lat) {
        let ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat +
                  0.1 * lng * lat + 0.2 * Math.sqrt(Math.abs(lng));
        ret += (20.0 * Math.sin(6.0 * lng * this.PI) + 20.0 * Math.sin(2.0 * lng * this.PI)) * 2.0 / 3.0;
        ret += (20.0 * Math.sin(lat * this.PI) + 40.0 * Math.sin(lat / 3.0 * this.PI)) * 2.0 / 3.0;
        ret += (160.0 * Math.sin(lat / 12.0 * this.PI) + 320 * Math.sin(lat * this.PI / 30.0)) * 2.0 / 3.0;
        return ret;
    }

    /**
     * 经度转换辅助函数
     * @private
     */
    _transformLng(lng, lat) {
        let ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng +
                  0.1 * lng * lat + 0.1 * Math.sqrt(Math.abs(lng));
        ret += (20.0 * Math.sin(6.0 * lng * this.PI) + 20.0 * Math.sin(2.0 * lng * this.PI)) * 2.0 / 3.0;
        ret += (20.0 * Math.sin(lng * this.PI) + 40.0 * Math.sin(lng / 3.0 * this.PI)) * 2.0 / 3.0;
        ret += (150.0 * Math.sin(lng / 12.0 * this.PI) + 300.0 * Math.sin(lng / 30.0 * this.PI)) * 2.0 / 3.0;
        return ret;
    }

    /**
     * 批量转换百度米制坐标为BD-09坐标
     * @param {Array} coordinates - 坐标数组，格式: [{x: number, y: number}, ...]
     * @returns {Array} BD-09坐标数组，格式: [{lng: number, lat: number}, ...]
     */
    batchMc2ll(coordinates) {
        return coordinates.map(coord => this.mc2ll(coord.x, coord.y));
    }

    /**
     * 批量转换BD-09坐标为WGS84坐标
     * @param {Array} coordinates - BD-09坐标数组，格式: [{lng: number, lat: number}, ...]
     * @returns {Array} WGS84坐标数组，格式: [{lng: number, lat: number}, ...]
     */
    batchBd09ToWgs84(coordinates) {
        return coordinates.map(coord => this.bd09ToWgs84(coord.lng, coord.lat));
    }

    /**
     * 一步转换：百度米制坐标直接转WGS84坐标
     * @param {number} x - 百度米制X坐标
     * @param {number} y - 百度米制Y坐标
     * @returns {Object} {lng: WGS84经度, lat: WGS84纬度}
     */
    mc2wgs84(x, y) {
        const bd09 = this.mc2ll(x, y);
        return this.bd09ToWgs84(bd09.lng, bd09.lat);
    }
}

/**
 * 解析坐标字符串
 * @param {string} coordString - 坐标字符串，如 "13019485.904418,4368445.639555"
 * @returns {Array} 坐标数组
 */
function parseCoordinateString(coordString) {
    const pattern = /(\d+\.?\d*),(\d+\.?\d*)/g;
    const coordinates = [];
    let match;
    
    while ((match = pattern.exec(coordString)) !== null) {
        coordinates.push({
            x: parseFloat(match[1]),
            y: parseFloat(match[2])
        });
    }
    
    return coordinates;
}
