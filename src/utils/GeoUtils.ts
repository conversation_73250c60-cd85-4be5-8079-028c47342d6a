/**
 * 地理空间计算工具类，提供点、线、面等空间关系判断及计算功能
 * 基于百度地图GL版(BMapGL)实现
 */

// 地球半径(米)
const EARTH_RADIUS = 6370996.81;

export class GeoUtils {
    /**
     * 判断点是否在矩形范围内
     * @param point 待判断的点
     * @param bounds 矩形边界
     * @returns 如果点在矩形内返回true，否则返回false
     */
    static isPointInRect(point: BMapGL.Point, bounds: BMapGL.Bounds): boolean {
        if (!(point instanceof BMapGL.Point) || !(bounds instanceof BMapGL.Bounds)) {
            return false;
        }

        const sw = bounds.getSouthWest(); // 西南角点
        const ne = bounds.getNorthEast(); // 东北角点

        // 检查点的经纬度是否在矩形范围内
        return point.lng >= sw.lng && point.lng <= ne.lng &&
            point.lat >= sw.lat && point.lat <= ne.lat;
    }

    /**
     * 判断点是否在圆形范围内
     * @param point 待判断的点
     * @param circle 圆形对象
     * @returns 如果点在圆内返回true，否则返回false
     */
    static isPointInCircle(point: BMapGL.Point, circle: BMapGL.Circle): boolean {
        if (!(point instanceof BMapGL.Point) || !(circle instanceof BMapGL.Circle)) {
            return false;
        }

        const center = circle.getCenter(); // 圆心
        const radius = circle.getRadius(); // 半径
        const distance = this.getDistance(point, center); // 点到圆心的距离

        return distance <= radius;
    }

    /**
     * 判断点是否在折线上
     * @param point 待判断的点
     * @param polyline 折线对象
     * @returns 如果点在折线上返回true，否则返回false
     */
    static isPointOnPolyline(point: BMapGL.Point, polyline: BMapGL.Polyline): boolean {
        if (!(point instanceof BMapGL.Point) || !(polyline instanceof BMapGL.Polyline)) {
            return false;
        }

        // 先检查点是否在折线的包围盒内，提高效率
        const bounds = polyline.getBounds();
        if (!this.isPointInRect(point, bounds)) {
            return false;
        }

        const path = polyline.getPath(); // 折线的点集合

        // 检查点是否在折线的任意一段上
        for (let i = 0; i < path.length - 1; i++) {
            const p1 = path[i];
            const p2 = path[i + 1];

            // 检查点是否在当前线段的包围盒内
            if (point.lng >= Math.min(p1.lng, p2.lng) && point.lng <= Math.max(p1.lng, p2.lng) &&
                point.lat >= Math.min(p1.lat, p2.lat) && point.lat <= Math.max(p1.lat, p2.lat)) {

                // 计算叉积，判断点是否在线段上
                const crossProduct = (p1.lng - point.lng) * (p2.lat - point.lat) -
                    (p2.lng - point.lng) * (p1.lat - point.lat);

                // 叉积接近0，认为点在线段上
                if (crossProduct < 2e-10 && crossProduct > -2e-10) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 判断点是否在多边形内
     * 使用射线法判断点与多边形的位置关系
     * @param point 待判断的点
     * @param polygon 多边形对象
     * @returns 如果点在多边形内返回true，否则返回false
     */
    static isPointInPolygon(point: BMapGL.Point, polygon: BMapGL.Polygon): boolean {
        if (!(point instanceof BMapGL.Point) || !(polygon instanceof BMapGL.Polygon)) {
            return false;
        }

        // 先检查点是否在多边形的包围盒内，提高效率
        const bounds = polygon.getBounds();
        if (!this.isPointInRect(point, bounds)) {
            return false;
        }

        const path = polygon.getPath(); // 多边形的顶点集合
        const pathLength = path.length;
        let isInside = true;
        let intersectionCount = 0;
        const epsilon = 2e-10; // 浮点数精度误差范围

        let p1: BMapGL.Point, p2: BMapGL.Point;
        p1 = path[0];

        // 遍历多边形的每条边
        for (let i = 1; i <= pathLength; i++) {
            // 点与多边形顶点重合，认为在多边形内
            if (point.equals(p1)) {
                return isInside;
            }

            p2 = path[i % pathLength];

            // 检查点的纬度是否不在当前边的纬度范围内
            if (point.lat < Math.min(p1.lat, p2.lat) || point.lat > Math.max(p1.lat, p2.lat)) {
                p1 = p2;
                continue;
            }

            // 点的纬度在当前边的纬度范围内
            if (point.lat > Math.min(p1.lat, p2.lat) && point.lat < Math.max(p1.lat, p2.lat)) {
                // 点的经度小于等于边的最大经度
                if (point.lng <= Math.max(p1.lng, p2.lng)) {
                    // 边是水平的
                    if (p1.lat === p2.lat && point.lng >= Math.min(p1.lng, p2.lng)) {
                        return isInside;
                    }

                    // 边是垂直的
                    if (p1.lng === p2.lng) {
                        if (p1.lng === point.lng) {
                            return isInside;
                        } else {
                            intersectionCount++;
                        }
                    } else {
                        // 计算射线与边的交点经度
                        const intersectionLng = ((point.lat - p1.lat) * (p2.lng - p1.lng)) /
                            (p2.lat - p1.lat) + p1.lng;

                        // 点在边上
                        if (Math.abs(point.lng - intersectionLng) < epsilon) {
                            return isInside;
                        }

                        // 射线与边相交
                        if (point.lng < intersectionLng) {
                            intersectionCount++;
                        }
                    }
                }
            } else {
                // 点的纬度等于边的一个端点纬度
                if (point.lat === p2.lat && point.lng <= p2.lng) {
                    const p3 = path[(i + 1) % pathLength];
                    if (point.lat >= Math.min(p1.lat, p3.lat) && point.lat <= Math.max(p1.lat, p3.lat)) {
                        intersectionCount++;
                    } else {
                        intersectionCount += 2;
                    }
                }
            }

            p1 = p2;
        }

        // 交点数量为奇数则点在多边形内
        return intersectionCount % 2 !== 0;
    }

    /**
     * 将角度转换为弧度
     * @param degree 角度值
     * @returns 对应的弧度值
     */
    static degreeToRad(degree: number): number {
        return (Math.PI * degree) / 180;
    }

    /**
     * 将弧度转换为角度
     * @param rad 弧度值
     * @returns 对应的角度值
     */
    static radToDegree(rad: number): number {
        return (180 * rad) / Math.PI;
    }

    /**
     * 计算两点之间的地球表面距离(米)
     * 使用Haversine公式计算球面距离
     * @param point1 第一个点
     * @param point2 第二个点
     * @returns 两点之间的距离(米)
     */
    static getDistance(point1: BMapGL.Point, point2: BMapGL.Point): number {
        if (!(point1 instanceof BMapGL.Point) || !(point2 instanceof BMapGL.Point)) {
            return 0;
        }

        // 规范化经度(-180至180)
        point1.lng = this.normalizeLng(point1.lng);
        point2.lng = this.normalizeLng(point2.lng);

        // 限制纬度范围(-74至74)
        point1.lat = this.clampLat(point1.lat);
        point2.lat = this.clampLat(point2.lat);

        // 转换为弧度
        const lng1 = this.degreeToRad(point1.lng);
        const lat1 = this.degreeToRad(point1.lat);
        const lng2 = this.degreeToRad(point2.lng);
        const lat2 = this.degreeToRad(point2.lat);

        // 应用Haversine公式
        return EARTH_RADIUS * Math.acos(
            Math.sin(lat1) * Math.sin(lat2) +
            Math.cos(lat1) * Math.cos(lat2) * Math.cos(lng2 - lng1)
        );
    }

    /**
     * 计算折线的总长度(米)
     * @param polyline 折线对象或点数组
     * @returns 折线的总长度(米)
     */
    static getPolylineDistance(polyline: BMapGL.Polyline | BMapGL.Point[]): number {
        let path: BMapGL.Point[];

        // 处理输入，获取点集合
        if (polyline instanceof BMapGL.Polyline) {
            path = polyline.getPath();
        } else if (Array.isArray(polyline)) {
            path = polyline;
        } else {
            return 0;
        }

        // 少于2个点的折线长度为0
        if (path.length < 2) {
            return 0;
        }

        let totalDistance = 0;

        // 累加每段线段的距离
        for (let i = 0; i < path.length - 1; i++) {
            totalDistance += this.getDistance(path[i], path[i + 1]);
        }

        return totalDistance;
    }

    /**
     * 计算多边形的面积(平方米)
     * @param polygon 多边形对象或点数组
     * @returns 多边形的面积(平方米)
     */
    static getPolygonArea(polygon: BMapGL.Polygon | BMapGL.Point[]): number {
        let path: BMapGL.Point[];

        // 处理输入，获取点集合
        if (polygon instanceof BMapGL.Polygon) {
            path = polygon.getPath();
        } else if (Array.isArray(polygon)) {
            path = polygon;
        } else {
            return 0;
        }

        // 少于3个点的多边形面积为0
        if (path.length < 3) {
            return 0;
        }

        let area = 0;
        const pathLength = path.length;

        // 球面多边形面积计算相关变量
        let sum1 = 0, sum2 = 0, count1 = 0, count2 = 0;
        let a: number, b: number, c: number, d: number, e: number, f: number;
        let g: number, h: number, i: number, j: number, k: number, l: number;
        let m: number, n: number, o: number, p: number, q: number, r: number;
        let s: number, t: number, u: number, v: number;
        let z: number;

        // 遍历多边形的每个顶点
        for (let idx = 0; idx < pathLength; idx++) {
            let prevLng, prevLat, currLng, currLat, nextLng, nextLat;

            // 获取当前点及其前后点的经纬度(弧度)
            if (idx === 0) {
                prevLng = (path[pathLength - 1].lng * Math.PI) / 180;
                prevLat = (path[pathLength - 1].lat * Math.PI) / 180;
                currLng = (path[0].lng * Math.PI) / 180;
                currLat = (path[0].lat * Math.PI) / 180;
                nextLng = (path[1].lng * Math.PI) / 180;
                nextLat = (path[1].lat * Math.PI) / 180;
            } else if (idx === pathLength - 1) {
                prevLng = (path[pathLength - 2].lng * Math.PI) / 180;
                prevLat = (path[pathLength - 2].lat * Math.PI) / 180;
                currLng = (path[pathLength - 1].lng * Math.PI) / 180;
                currLat = (path[pathLength - 1].lat * Math.PI) / 180;
                nextLng = (path[0].lng * Math.PI) / 180;
                nextLat = (path[0].lat * Math.PI) / 180;
            } else {
                prevLng = (path[idx - 1].lng * Math.PI) / 180;
                prevLat = (path[idx - 1].lat * Math.PI) / 180;
                currLng = (path[idx].lng * Math.PI) / 180;
                currLat = (path[idx].lat * Math.PI) / 180;
                nextLng = (path[idx + 1].lng * Math.PI) / 180;
                nextLat = (path[idx + 1].lat * Math.PI) / 180;
            }

            // 计算当前点的三维坐标
            a = Math.cos(currLat) * Math.cos(currLng);
            b = Math.cos(currLat) * Math.sin(currLng);
            c = Math.sin(currLat);

            // 计算前一个点的三维坐标
            d = Math.cos(prevLat) * Math.cos(prevLng);
            e = Math.cos(prevLat) * Math.sin(prevLng);
            f = Math.sin(prevLat);

            // 计算后一个点的三维坐标
            g = Math.cos(nextLat) * Math.cos(nextLng);
            h = Math.cos(nextLat) * Math.sin(nextLng);
            i = Math.sin(nextLat);

            // 计算向量
            j = (a * a + b * b + c * c) / (a * d + b * e + c * f);
            k = (a * a + b * b + c * c) / (a * g + b * h + c * i);

            l = j * d - a;
            m = j * e - b;
            n = j * f - c;

            o = k * g - a;
            p = k * h - b;
            q = k * i - c;

            // 计算角度
            r = (o * l + p * m + q * n) /
                (Math.sqrt(o * o + p * p + q * q) * Math.sqrt(l * l + m * m + n * n));
            r = Math.acos(r);

            // 计算法向量
            s = p * n - q * m;
            t = -(o * n - q * l);
            u = o * m - p * l;

            // 判断方向
            v = 0;
            if (a !== 0) {
                v = s / a;
            } else if (b !== 0) {
                v = t / b;
            } else {
                v = u / c;
            }

            if (v > 0) {
                sum1 += r;
                count1++;
            } else {
                sum2 += r;
                count2++;
            }
        }

        // 计算球面多边形面积
        const option1 = sum1 + (2 * Math.PI * count2 - sum2);
        const option2 = 2 * Math.PI * count1 - sum1 + sum2;

        if (sum1 > sum2) {
            z = (option1 - (pathLength - 2) * Math.PI < 1) ? option1 : option2;
        } else {
            z = (option2 - (pathLength - 2) * Math.PI < 1) ? option2 : option1;
        }

        area = (z - (pathLength - 2) * Math.PI) * EARTH_RADIUS * EARTH_RADIUS;

        return area;
    }

    /**
     * 判断折线是否与多边形有交集
     * @param polyline 折线对象
     * @param polygon 多边形对象
     * @returns 如果折线与多边形有交集返回true，否则返回false
     */
    static isPolylineIntersectPolygon(polyline: BMapGL.Polyline, polygon: BMapGL.Polygon): boolean {
        if (!(polyline instanceof BMapGL.Polyline) || !(polygon instanceof BMapGL.Polygon)) {
            return false;
        }

        const polylinePath = polyline.getPath();
        const polygonPath = polygon.getPath();

        // 检查折线的任意一点是否在多边形内
        for (const point of polylinePath) {
            if (this.isPointInPolygon(point, polygon)) {
                return true;
            }
        }

        // 检查折线的任意一段是否与多边形的边相交
        for (let i = 0; i < polylinePath.length - 1; i++) {
            const lineStart = polylinePath[i];
            const lineEnd = polylinePath[i + 1];

            // 检查当前线段是否与多边形的任意一边相交
            for (let j = 0; j < polygonPath.length; j++) {
                const polygonStart = polygonPath[j];
                const polygonEnd = polygonPath[(j + 1) % polygonPath.length];

                if (this.isLineSegmentIntersect(lineStart, lineEnd, polygonStart, polygonEnd)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 判断两条线段是否相交
     * @param p1 第一条线段的起点
     * @param p2 第一条线段的终点
     * @param p3 第二条线段的起点
     * @param p4 第二条线段的终点
     * @returns 如果两条线段相交返回true，否则返回false
     */
    static isLineSegmentIntersect(
        p1: BMapGL.Point,
        p2: BMapGL.Point,
        p3: BMapGL.Point,
        p4: BMapGL.Point
    ): boolean {
        if (!(p1 instanceof BMapGL.Point) || !(p2 instanceof BMapGL.Point) ||
            !(p3 instanceof BMapGL.Point) || !(p4 instanceof BMapGL.Point)) {
            return false;
        }

        // 计算向量
        const d1 = this.crossProduct(p3, p4, p1);
        const d2 = this.crossProduct(p3, p4, p2);
        const d3 = this.crossProduct(p1, p2, p3);
        const d4 = this.crossProduct(p1, p2, p4);

        // 判断是否跨立
        if (((d1 > 0 && d2 < 0) || (d1 < 0 && d2 > 0)) &&
            ((d3 > 0 && d4 < 0) || (d3 < 0 && d4 > 0))) {
            return true;
        }

        // 判断是否有端点在另一条线段上
        if (d1 === 0 && this.isPointOnLineSegment(p1, p3, p4)) return true;
        if (d2 === 0 && this.isPointOnLineSegment(p2, p3, p4)) return true;
        if (d3 === 0 && this.isPointOnLineSegment(p3, p1, p2)) return true;
        if (d4 === 0 && this.isPointOnLineSegment(p4, p1, p2)) return true;

        return false;
    }

    /**
     * 计算向量叉积
     * @param lineStart 线段起点
     * @param lineEnd 线段终点
     * @param point 待计算的点
     * @returns 叉积值
     */
    private static crossProduct(lineStart: BMapGL.Point, lineEnd: BMapGL.Point, point: BMapGL.Point): number {
        return (lineEnd.lng - lineStart.lng) * (point.lat - lineStart.lat) -
            (lineEnd.lat - lineStart.lat) * (point.lng - lineStart.lng);
    }

    /**
     * 判断点是否在线段上
     * @param point 待判断的点
     * @param lineStart 线段起点
     * @param lineEnd 线段终点
     * @returns 如果点在线段上返回true，否则返回false
     */
    private static isPointOnLineSegment(
        point: BMapGL.Point,
        lineStart: BMapGL.Point,
        lineEnd: BMapGL.Point
    ): boolean {
        // 检查点是否在线段的包围盒内
        return point.lng >= Math.min(lineStart.lng, lineEnd.lng) &&
            point.lng <= Math.max(lineStart.lng, lineEnd.lng) &&
            point.lat >= Math.min(lineStart.lat, lineEnd.lat) &&
            point.lat <= Math.max(lineStart.lat, lineEnd.lat);
    }

    /**
     * 规范化经度值到[-180, 180]范围
     * @param lng 经度值
     * @returns 规范化后的经度值
     */
    private static normalizeLng(lng: number): number {
        let normalizedLng = lng;
        while (normalizedLng > 180) {
            normalizedLng -= 360;
        }
        while (normalizedLng < -180) {
            normalizedLng += 360;
        }
        return normalizedLng;
    }

    /**
     * 将纬度值限制在[-74, 74]范围
     * @param lat 纬度值
     * @returns 限制后的纬度值
     */
    private static clampLat(lat: number): number {
        return Math.max(Math.min(lat, 74), -74);
    }

    /**
     * 计算线段与多边形边界的交点
     * @param lineStart 线段起点
     * @param lineEnd 线段终点
     * @param polygonStart 多边形边起点
     * @param polygonEnd 多边形边终点
     * @returns 交点坐标，如果没有交点返回null
     */
    static getLineSegmentIntersection(
        lineStart: BMapGL.Point,
        lineEnd: BMapGL.Point,
        polygonStart: BMapGL.Point,
        polygonEnd: BMapGL.Point
    ): BMapGL.Point | null {
        const x1 = lineStart.lng, y1 = lineStart.lat;
        const x2 = lineEnd.lng, y2 = lineEnd.lat;
        const x3 = polygonStart.lng, y3 = polygonStart.lat;
        const x4 = polygonEnd.lng, y4 = polygonEnd.lat;

        const denom = (x1 - x2) * (y3 - y4) - (y1 - y2) * (x3 - x4);

        // 平行线，无交点
        if (Math.abs(denom) < 1e-10) {
            return null;
        }

        const t = ((x1 - x3) * (y3 - y4) - (y1 - y3) * (x3 - x4)) / denom;
        const u = -((x1 - x2) * (y1 - y3) - (y1 - y2) * (x1 - x3)) / denom;

        // 检查交点是否在两条线段上
        if (t >= 0 && t <= 1 && u >= 0 && u <= 1) {
            const intersectionX = x1 + t * (x2 - x1);
            const intersectionY = y1 + t * (y2 - y1);
            return new BMapGL.Point(intersectionX, intersectionY);
        }

        return null;
    }

    /**
     * 将线段按照与多边形的交点进行分割，返回分割后的线段及其在多边形内外的状态
     * @param line 线段点数组
     * @param polygon 多边形对象
     * @returns 分割后的线段数组，每个元素包含点数组和是否在多边形内的标志
     */
    static splitLineByPolygonIntersection(
        line: BMapGL.Point[],
        polygon: BMapGL.Polygon | null
    ): { points: BMapGL.Point[]; isInRegion: boolean }[] {
        if (!polygon || line.length < 2) {
            return [{ points: line, isInRegion: true }];
        }

        const polygonPath = polygon.getPath();
        const segments: { points: BMapGL.Point[]; isInRegion: boolean }[] = [];

        let currentSegment: BMapGL.Point[] = [line[0]];
        let currentIsInRegion = this.isPointInPolygon(line[0], polygon);

        for (let i = 0; i < line.length - 1; i++) {
            const lineStart = line[i];
            const lineEnd = line[i + 1];
            const intersections: { point: BMapGL.Point; t: number }[] = [];

            // 检查当前线段与多边形所有边的交点
            for (let j = 0; j < polygonPath.length; j++) {
                const polygonStart = polygonPath[j];
                const polygonEnd = polygonPath[(j + 1) % polygonPath.length];

                const intersection = this.getLineSegmentIntersection(
                    lineStart, lineEnd, polygonStart, polygonEnd
                );

                if (intersection) {
                    // 计算交点在线段上的参数t (0到1之间)
                    const dx = lineEnd.lng - lineStart.lng;
                    const dy = lineEnd.lat - lineStart.lat;
                    let t: number;

                    if (Math.abs(dx) > Math.abs(dy)) {
                        t = (intersection.lng - lineStart.lng) / dx;
                    } else {
                        t = (intersection.lat - lineStart.lat) / dy;
                    }

                    // 确保t在有效范围内，并且不是端点
                    if (t > 0.001 && t < 0.999) {
                        intersections.push({ point: intersection, t });
                    }
                }
            }

            // 按t值排序交点
            intersections.sort((a, b) => a.t - b.t);

            // 处理当前线段上的所有交点
            for (const intersection of intersections) {
                // 添加交点到当前段作为结束点
                currentSegment.push(intersection.point);

                // 结束当前段
                if (currentSegment.length >= 2) {
                    segments.push({
                        points: [...currentSegment],
                        isInRegion: currentIsInRegion
                    });
                }

                // 开始新段，交点作为新段的起点
                currentSegment = [intersection.point];
                currentIsInRegion = !currentIsInRegion; // 跨越边界，状态翻转
            }

            // 添加线段终点到当前段
            currentSegment.push(lineEnd);
        }

        // 添加最后一段
        if (currentSegment.length >= 2) {
            segments.push({
                points: currentSegment,
                isInRegion: currentIsInRegion
            });
        }

        return segments.length > 0 ? segments : [{ points: line, isInRegion: true }];
    }
}

